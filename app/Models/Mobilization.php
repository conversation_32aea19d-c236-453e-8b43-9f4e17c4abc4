<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Mobilization extends Model
{
    use HasFactory;

    protected $table = 'mobilizations';

    protected $fillable = [
        'name',
        'description',
        'start_date',
        'end_date',
        'type',
        'status',
        'location',
        'target_participants',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    /**
     * Relationship with mobilization participations
     */
    public function mobilizationParticipations(): HasMany
    {
        return $this->hasMany(MobilizationParticipation::class, 'mobilization_id');
    }

    /**
     * Relationship with people
     */
    public function people(): BelongsToMany
    {
        return $this->belongsToMany(Person::class, 'mobilization_participations', 'mobilization_id', 'person_id')
            ->withPivot(['participation_status', 'confirmation_date', 'attendance_date', 'notes'])
            ->withTimestamps();
    }

    /**
     * Scope for active mobilizations
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['planned', 'in_progress']);
    }

    // Legacy compatibility methods
    public function participacionesMovilizacion(): HasMany
    {
        return $this->mobilizationParticipations();
    }

    public function personas(): BelongsToMany
    {
        return $this->people();
    }

    public function scopeActivas($query)
    {
        return $this->scopeActive($query);
    }
}
