<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Person extends Model
{
    use HasFactory;

    protected $table = 'people';

    protected $fillable = [
        'first_name',
        'last_name',
        'document_number',
        'birth_date',
        'gender',
        'phone',
        'secondary_phone',
        'email',
        'address',
        'state_id',
        'municipality_id',
        'parish_id',
        'voting_center_id',
        'voting_table',
        'person_type',
        'is_leader_1x10',
        'assigned_leader_id',
        'user_id',
        'status',
        'notes',
        'additional_data',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'is_leader_1x10' => 'boolean',
        'additional_data' => 'array',
    ];

    /**
     * Relationship with geographic state
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'estado_id');
    }

    /**
     * Relationship with municipality
     */
    public function municipality(): BelongsTo
    {
        return $this->belongsTo(Municipality::class, 'municipio_id');
    }

    /**
     * Relationship with parish
     */
    public function parish(): BelongsTo
    {
        return $this->belongsTo(Parish::class, 'parroquia_id');
    }

    /**
     * Relationship with voting center
     */
    public function votingCenter(): BelongsTo
    {
        return $this->belongsTo(VotingCenter::class, 'centro_votacion_id');
    }

    /**
     * Relationship with assigned leader
     */
    public function assignedLeader(): BelongsTo
    {
        return $this->belongsTo(Person::class, 'lider_asignado_id');
    }

    /**
     * Relationship with assigned people (for leaders)
     */
    public function assignedPeople(): HasMany
    {
        return $this->hasMany(Person::class, 'lider_asignado_id');
    }

    /**
     * Relationship with system user
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relationship with electoral events
     */
    public function electoralEvents(): BelongsToMany
    {
        return $this->belongsToMany(ElectoralEvent::class, 'participaciones', 'persona_id', 'evento_electoral_id')
            ->withPivot(['tipo_participacion', 'confirmo_participacion', 'fecha_confirmacion', 'observaciones'])
            ->withTimestamps();
    }

    /**
     * Relationship with mobilizations
     */
    public function mobilizations(): BelongsToMany
    {
        return $this->belongsToMany(Mobilization::class, 'participaciones_movilizacion', 'persona_id', 'movilizacion_id')
            ->withPivot(['estado_participacion', 'fecha_confirmacion', 'fecha_asistencia', 'observaciones'])
            ->withTimestamps();
    }

    /**
     * Accessor for full name
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->nombres . ' ' . $this->apellidos);
    }

    /**
     * Accessor for age
     */
    public function getAgeAttribute(): ?int
    {
        return $this->fecha_nacimiento ? $this->fecha_nacimiento->age : null;
    }

    /**
     * Scope for active people
     */
    public function scopeActive($query)
    {
        return $query->where('estado', 'activo');
    }

    /**
     * Scope for militants
     */
    public function scopeMilitants($query)
    {
        return $query->where('tipo_persona', 'militante');
    }

    /**
     * Scope for voters
     */
    public function scopeVoters($query)
    {
        return $query->where('tipo_persona', 'votante');
    }

    /**
     * Scope for sympathizers
     */
    public function scopeSympathizers($query)
    {
        return $query->where('tipo_persona', 'simpatizante');
    }

    /**
     * Scope for 1x10 leaders
     */
    public function scopeLeaders1x10($query)
    {
        return $query->where('es_lider_1x10', true);
    }

    /**
     * Scope for text search
     */
    public function scopeSearchText($query, $text)
    {
        return $query->where(function ($q) use ($text) {
            $q->where('nombres', 'like', "%{$text}%")
              ->orWhere('apellidos', 'like', "%{$text}%")
              ->orWhere('cedula', 'like', "%{$text}%")
              ->orWhere('email', 'like', "%{$text}%")
              ->orWhere('telefono', 'like', "%{$text}%");
        });
    }

    /**
     * Check if can be 1x10 leader
     */
    public function canBe1x10Leader(): bool
    {
        return $this->assignedPeople()->count() < 10;
    }

    /**
     * Get available spaces for 1x10 leader
     */
    public function getAvailableSpaces(): int
    {
        return 10 - $this->assignedPeople()->count();
    }

    /**
     * Check if has system user
     */
    public function hasSystemUser(): bool
    {
        return !is_null($this->user_id);
    }

    /**
     * Get person type in Spanish for compatibility
     */
    public function getPersonTypeSpanishAttribute(): string
    {
        $types = [
            'militant' => 'militante',
            'voter' => 'votante',
            'sympathizer' => 'simpatizante',
        ];

        return $types[$this->person_type] ?? $this->person_type;
    }

    /**
     * Get status in Spanish for compatibility
     */
    public function getStatusSpanishAttribute(): string
    {
        $statuses = [
            'active' => 'activo',
            'inactive' => 'inactivo',
            'suspended' => 'suspendido',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    // Spanish compatibility methods
    public function estadoGeografico(): BelongsTo
    {
        return $this->state();
    }

    public function estado(): BelongsTo
    {
        return $this->state();
    }

    public function municipio(): BelongsTo
    {
        return $this->municipality();
    }

    public function parroquia(): BelongsTo
    {
        return $this->parish();
    }

    public function centroVotacion(): BelongsTo
    {
        return $this->votingCenter();
    }

    public function liderAsignado(): BelongsTo
    {
        return $this->assignedLeader();
    }

    public function personasAsignadas(): HasMany
    {
        return $this->assignedPeople();
    }

    public function eventosElectorales(): BelongsToMany
    {
        return $this->electoralEvents();
    }

    public function movilizaciones(): BelongsToMany
    {
        return $this->mobilizations();
    }

    public function getNombreCompletoAttribute(): string
    {
        return $this->getFullNameAttribute();
    }

    public function getEdadAttribute(): ?int
    {
        return $this->getAgeAttribute();
    }

    public function scopeActivas($query)
    {
        return $this->scopeActive($query);
    }

    public function scopeMilitantes($query)
    {
        return $this->scopeMilitants($query);
    }

    public function scopeVotantes($query)
    {
        return $this->scopeVoters($query);
    }

    public function scopeLideres1x10($query)
    {
        return $this->scopeLeaders1x10($query);
    }

    public function scopeBuscarTexto($query, $texto)
    {
        return $this->scopeSearchText($query, $texto);
    }

    public function puedeSerLider1x10(): bool
    {
        return $this->canBe1x10Leader();
    }

    public function espaciosDisponiblesLider(): int
    {
        return $this->getAvailableSpaces();
    }
}
