<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Parish extends Model
{
    use HasFactory;

    protected $table = 'parishes';

    protected $fillable = [
        'municipality_id',
        'name',
        'code',
        'active',
    ];

    protected $casts = [
        'active' => 'boolean',
    ];

    /**
     * Relationship with municipality
     */
    public function municipality(): BelongsTo
    {
        return $this->belongsTo(Municipality::class, 'municipality_id');
    }

    /**
     * Relationship with voting centers
     */
    public function votingCenters(): HasMany
    {
        return $this->hasMany(VotingCenter::class, 'parish_id');
    }

    /**
     * Relationship with people
     */
    public function people(): HasMany
    {
        return $this->hasMany(Person::class, 'parish_id');
    }

    /**
     * Scope for active parishes
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    // Legacy compatibility methods
    public function municipio(): BelongsTo
    {
        return $this->municipality();
    }

    public function centrosVotacion(): Has<PERSON>any
    {
        return $this->votingCenters();
    }

    public function personas(): <PERSON><PERSON><PERSON>
    {
        return $this->people();
    }

    public function scopeActivos($query)
    {
        return $this->scopeActive($query);
    }
}
