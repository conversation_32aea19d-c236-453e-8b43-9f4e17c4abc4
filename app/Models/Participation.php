<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Participation extends Model
{
    use HasFactory;

    protected $table = 'participations';

    protected $fillable = [
        'person_id',
        'electoral_event_id',
        'participation_type',
        'confirmed_participation',
        'confirmation_date',
        'notes',
    ];

    protected $casts = [
        'confirmed_participation' => 'boolean',
        'confirmation_date' => 'datetime',
    ];

    /**
     * Relationship with person
     */
    public function person(): BelongsTo
    {
        return $this->belongsTo(Person::class, 'person_id');
    }

    /**
     * Relationship with electoral event
     */
    public function electoralEvent(): BelongsTo
    {
        return $this->belongsTo(ElectoralEvent::class, 'electoral_event_id');
    }

    // Legacy compatibility methods
    public function persona(): BelongsTo
    {
        return $this->person();
    }

    public function eventoElectoral(): BelongsTo
    {
        return $this->electoralEvent();
    }
}
