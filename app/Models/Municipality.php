<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Municipality extends Model
{
    use HasFactory;

    protected $table = 'municipalities';

    protected $fillable = [
        'state_id',
        'name',
        'code',
        'active',
    ];

    protected $casts = [
        'active' => 'boolean',
    ];

    /**
     * Relationship with state
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    /**
     * Relationship with parishes
     */
    public function parishes(): HasMany
    {
        return $this->hasMany(Parish::class, 'municipality_id');
    }

    /**
     * Relationship with people
     */
    public function people(): Has<PERSON>any
    {
        return $this->hasMany(Person::class, 'municipality_id');
    }

    /**
     * Scope for active municipalities
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    // Legacy compatibility methods
    public function estado(): BelongsTo
    {
        return $this->state();
    }

    public function parroquias(): <PERSON><PERSON><PERSON>
    {
        return $this->parishes();
    }

    public function personas(): <PERSON><PERSON><PERSON>
    {
        return $this->people();
    }

    public function scopeActivos($query)
    {
        return $this->scopeActive($query);
    }
}
