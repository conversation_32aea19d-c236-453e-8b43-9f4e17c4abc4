<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class State extends Model
{
    use HasFactory;

    protected $table = 'states';

    protected $fillable = [
        'name',
        'code',
        'active',
    ];

    protected $casts = [
        'active' => 'boolean',
    ];

    /**
     * Relationship with municipalities
     */
    public function municipalities(): HasMany
    {
        return $this->hasMany(Municipality::class, 'state_id');
    }

    /**
     * Relationship with people
     */
    public function people(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Person::class, 'state_id');
    }

    /**
     * Scope for active states
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    // Legacy compatibility methods
    public function municipios(): HasMany
    {
        return $this->municipalities();
    }

    public function personas(): <PERSON><PERSON><PERSON>
    {
        return $this->people();
    }

    public function scopeActivos($query)
    {
        return $this->scopeActive($query);
    }
}
