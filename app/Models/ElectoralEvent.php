<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class ElectoralEvent extends Model
{
    use HasFactory;

    protected $table = 'electoral_events';

    protected $fillable = [
        'name',
        'description',
        'event_date',
        'type',
        'status',
    ];

    protected $casts = [
        'event_date' => 'date',
    ];

    /**
     * Relationship with participations
     */
    public function participations(): HasMany
    {
        return $this->hasMany(Participation::class, 'electoral_event_id');
    }

    /**
     * Relationship with people
     */
    public function people(): BelongsToMany
    {
        return $this->belongsToMany(Person::class, 'participations', 'electoral_event_id', 'person_id')
            ->withPivot(['participation_type', 'confirmed_participation', 'confirmation_date', 'notes'])
            ->withTimestamps();
    }

    /**
     * Scope for active events
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['scheduled', 'in_progress']);
    }

    // Legacy compatibility methods
    public function participaciones(): HasMany
    {
        return $this->participations();
    }

    public function personas(): BelongsToMany
    {
        return $this->people();
    }

    public function scopeActivos($query)
    {
        return $this->scopeActive($query);
    }
}
