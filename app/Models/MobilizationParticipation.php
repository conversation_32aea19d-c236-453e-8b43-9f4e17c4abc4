<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MobilizationParticipation extends Model
{
    use HasFactory;

    protected $table = 'mobilization_participations';

    protected $fillable = [
        'person_id',
        'mobilization_id',
        'participation_status',
        'confirmation_date',
        'attendance_date',
        'notes',
    ];

    protected $casts = [
        'confirmation_date' => 'datetime',
        'attendance_date' => 'datetime',
    ];

    /**
     * Relationship with person
     */
    public function person(): BelongsTo
    {
        return $this->belongsTo(Person::class, 'person_id');
    }

    /**
     * Relationship with mobilization
     */
    public function mobilization(): BelongsTo
    {
        return $this->belongsTo(Mobilization::class, 'mobilization_id');
    }

    // Legacy compatibility methods
    public function persona(): BelongsTo
    {
        return $this->person();
    }

    public function movilizacion(): BelongsTo
    {
        return $this->mobilization();
    }
}
