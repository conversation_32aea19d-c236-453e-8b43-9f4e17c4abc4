<?php

namespace App\Livewire\Admin\People\Components;

use App\Models\VotingCenter;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use App\Models\Person;
use Illuminate\Contracts\View\View;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Session;
use Livewire\Component;
use Livewire\WithPagination;

class AdvancedSearch extends Component
{
    use LivewireAlert;
    use WithPagination;

    #[Session]
    public int $perPage = 25;

    // Search filters
    public string $firstName = '';
    public string $lastName = '';
    public string $documentNumber = '';
    public string $email = '';
    public string $phone = '';

    // Location filters
    public ?int $stateId = null;
    public ?int $municipalityId = null;
    public ?int $parishId = null;
    public ?int $votingCenterId = null;

    // Classification filters
    public string $personType = '';
    public string $personStatus = '';
    public string $isLeader1x10 = '';
    public string $hasLeader = '';
    public string $hasUser = '';

    // Date filters
    public ?string $birthDateFrom = null;
    public ?string $birthDateTo = null;
    public ?string $registrationDateFrom = null;
    public ?string $registrationDateTo = null;

    // Age filters
    public ?int $minAge = null;
    public ?int $maxAge = null;

    public bool $showResults = false;

    public function mount(): void
    {
        $this->authorize('view people');
    }

    public function updatedStateId(): void
    {
        $this->municipalityId = null;
        $this->parishId = null;
        $this->votingCenterId = null;
        $this->resetPage();
    }

    public function updatedMunicipalityId(): void
    {
        $this->parishId = null;
        $this->votingCenterId = null;
        $this->resetPage();
    }

    public function updatedParishId(): void
    {
        $this->votingCenterId = null;
        $this->resetPage();
    }

    public function search(): void
    {
        $this->showResults = true;
        $this->resetPage();
    }

    public function clearFilters(): void
    {
        $this->reset([
            'firstName', 'lastName', 'documentNumber', 'email', 'phone',
            'stateId', 'municipalityId', 'parishId', 'votingCenterId',
            'personType', 'personStatus', 'isLeader1x10', 'hasLeader', 'hasUser',
            'birthDateFrom', 'birthDateTo',
            'registrationDateFrom', 'registrationDateTo',
            'minAge', 'maxAge'
        ]);
        $this->showResults = false;
        $this->resetPage();
    }

    public function exportResults(): void
    {
        $this->authorize('export people');

        // Here you would implement the export logic
        $this->alert('info', __('people.export_in_development'));
    }

    public function getMunicipalitiesProperty()
    {
        if (!$this->stateId) {
            return collect();
        }

        return Municipality::where('state_id', $this->stateId)
            ->active()
            ->orderBy('name')
            ->get();
    }

    public function getParishesProperty()
    {
        if (!$this->municipalityId) {
            return collect();
        }

        return Parish::where('municipality_id', $this->municipalityId)
            ->active()
            ->orderBy('name')
            ->get();
    }

    public function getVotingCentersProperty()
    {
        if (!$this->parishId) {
            return collect();
        }

        return VotingCenter::where('parish_id', $this->parishId)
            ->active()
            ->orderBy('name')
            ->get();
    }

    public function getPeopleProperty()
    {
        if (!$this->showResults) {
            return collect();
        }

        $query = Person::query()
            ->with(['state', 'municipality', 'parish', 'votingCenter', 'assignedLeader', 'user']);

        // Text filters
        if ($this->firstName) {
            $query->where('first_name', 'like', "%{$this->firstName}%");
        }

        if ($this->lastName) {
            $query->where('last_name', 'like', "%{$this->lastName}%");
        }

        if ($this->documentNumber) {
            $query->where('document_number', 'like', "%{$this->documentNumber}%");
        }

        if ($this->email) {
            $query->where('email', 'like', "%{$this->email}%");
        }

        if ($this->phone) {
            $query->where(function ($q) {
                $q->where('phone', 'like', "%{$this->phone}%")
                  ->orWhere('secondary_phone', 'like', "%{$this->phone}%");
            });
        }

        // Location filters
        if ($this->stateId) {
            $query->where('state_id', $this->stateId);
        }

        if ($this->municipalityId) {
            $query->where('municipality_id', $this->municipalityId);
        }

        if ($this->parishId) {
            $query->where('parish_id', $this->parishId);
        }

        if ($this->votingCenterId) {
            $query->where('voting_center_id', $this->votingCenterId);
        }

        // Classification filters
        if ($this->personType) {
            $query->where('person_type', $this->personType);
        }

        if ($this->personStatus) {
            $query->where('status', $this->personStatus);
        }

        if ($this->isLeader1x10 !== '') {
            $query->where('is_leader_1x10', $this->isLeader1x10 === '1');
        }

        if ($this->hasLeader !== '') {
            if ($this->hasLeader === '1') {
                $query->whereNotNull('assigned_leader_id');
            } else {
                $query->whereNull('assigned_leader_id');
            }
        }

        if ($this->hasUser !== '') {
            if ($this->hasUser === '1') {
                $query->whereNotNull('user_id');
            } else {
                $query->whereNull('user_id');
            }
        }

        // Date filters
        if ($this->birthDateFrom) {
            $query->where('birth_date', '>=', $this->birthDateFrom);
        }

        if ($this->birthDateTo) {
            $query->where('birth_date', '<=', $this->birthDateTo);
        }

        if ($this->registrationDateFrom) {
            $query->where('created_at', '>=', $this->registrationDateFrom);
        }

        if ($this->registrationDateTo) {
            $query->where('created_at', '<=', $this->registrationDateTo . ' 23:59:59');
        }

        // Age filters
        if ($this->minAge) {
            $query->whereRaw('TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) >= ?', [$this->minAge]);
        }

        if ($this->maxAge) {
            $query->whereRaw('TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) <= ?', [$this->maxAge]);
        }

        return $query->orderBy('first_name')
            ->orderBy('last_name')
            ->paginate($this->perPage);
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.people.components.advanced-search', [
            'states' => State::active()->orderBy('name')->get(),
            'municipalities' => $this->municipalities,
            'parishes' => $this->parishes,
            'votingCenters' => $this->votingCenters,
            'people' => $this->people,
        ]);
    }
}
