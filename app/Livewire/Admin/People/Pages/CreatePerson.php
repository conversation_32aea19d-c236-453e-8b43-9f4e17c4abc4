<?php

namespace App\Livewire\Admin\People\Pages;

use App\Models\VotingCenter;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use App\Models\Person;
use Illuminate\Contracts\View\View;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Livewire\Component;

class CreatePerson extends Component
{
    use LivewireAlert;

    // Personal data
    #[Validate('required|string|max:255')]
    public string $firstName = '';

    #[Validate('required|string|max:255')]
    public string $lastName = '';

    #[Validate('required|string|max:20|unique:people,document_number')]
    public string $documentNumber = '';

    #[Validate('nullable|date|before:today')]
    public ?string $birthDate = null;

    #[Validate('nullable|in:M,F,O')]
    public ?string $gender = null;

    // Contact information
    #[Validate('nullable|string|max:20')]
    public ?string $phone = null;

    #[Validate('nullable|string|max:20')]
    public ?string $secondaryPhone = null;

    #[Validate('nullable|email|max:255')]
    public ?string $email = null;

    #[Validate('nullable|string|max:500')]
    public ?string $address = null;

    // Location
    #[Validate('nullable|exists:states,id')]
    public ?int $stateId = null;

    #[Validate('nullable|exists:municipalities,id')]
    public ?int $municipalityId = null;

    #[Validate('nullable|exists:parishes,id')]
    public ?int $parishId = null;

    // Electoral information
    #[Validate('nullable|exists:voting_centers,id')]
    public ?int $votingCenterId = null;

    #[Validate('nullable|string|max:10')]
    public ?string $votingTable = null;

    // System role
    #[Validate('required|in:militant,voter,sympathizer')]
    public string $personType = 'voter';

    #[Validate('boolean')]
    public bool $isLeader1x10 = false;

    #[Validate('nullable|exists:people,id')]
    public ?int $assignedLeaderId = null;

    // Status and notes
    #[Validate('required|in:active,inactive,suspended')]
    public string $status = 'active';

    #[Validate('nullable|string|max:1000')]
    public ?string $notes = null;

    public function mount(): void
    {
        $this->authorize('create people');
    }

    public function updatedStateId(): void
    {
        $this->municipalityId = null;
        $this->parishId = null;
        $this->votingCenterId = null;
    }

    public function updatedMunicipalityId(): void
    {
        $this->parishId = null;
        $this->votingCenterId = null;
    }

    public function updatedParishId(): void
    {
        $this->votingCenterId = null;
    }

    public function updatedDocumentNumber(): void
    {
        // Check if a person with this document number already exists
        if ($this->documentNumber && Person::where('document_number', $this->documentNumber)->exists()) {
            $this->addError('documentNumber', __('people.document_number_already_exists'));
        }
    }

    public function updatedEmail(): void
    {
        // Check if a person with this email already exists
        if ($this->email && Person::where('email', $this->email)->exists()) {
            $this->addError('email', __('people.email_already_exists'));
        }
    }

    public function updatedAssignedLeaderId(): void
    {
        if ($this->assignedLeaderId) {
            $leader = Person::find($this->assignedLeaderId);
            if ($leader && !$leader->canBe1x10Leader()) {
                $this->addError('assignedLeaderId', __('people.leader_no_spaces'));
                $this->assignedLeaderId = null;
            }
        }
    }

    public function createPerson(): void
    {
        $this->authorize('create people');

        $this->validate();

        // Additional validations
        if ($this->assignedLeaderId) {
            $leader = Person::find($this->assignedLeaderId);
            if (!$leader || !$leader->canBe1x10Leader()) {
                $this->addError('assignedLeaderId', __('people.leader_no_spaces'));
                return;
            }
        }

        try {
            $person = Person::create([
                'first_name' => $this->firstName,
                'last_name' => $this->lastName,
                'document_number' => $this->documentNumber,
                'birth_date' => $this->birthDate,
                'gender' => $this->gender,
                'phone' => $this->phone,
                'secondary_phone' => $this->secondaryPhone,
                'email' => $this->email,
                'address' => $this->address,
                'state_id' => $this->stateId,
                'municipality_id' => $this->municipalityId,
                'parish_id' => $this->parishId,
                'voting_center_id' => $this->votingCenterId,
                'voting_table' => $this->votingTable,
                'person_type' => $this->personType,
                'is_leader_1x10' => $this->isLeader1x10,
                'assigned_leader_id' => $this->assignedLeaderId,
                'status' => $this->status,
                'notes' => $this->notes,
            ]);

            $this->flash('success', __('people.person_created'));
            $this->redirect(route('admin.people.show', $person), navigate: true);

        } catch (\Exception $e) {
            $this->alert('error', __('people.error_creating_person'));
        }
    }

    public function getMunicipalitiesProperty()
    {
        if (!$this->stateId) {
            return collect();
        }

        return Municipality::where('state_id', $this->stateId)
            ->active()
            ->orderBy('name')
            ->get();
    }

    public function getParishesProperty()
    {
        if (!$this->municipalityId) {
            return collect();
        }

        return Parish::where('municipality_id', $this->municipalityId)
            ->active()
            ->orderBy('name')
            ->get();
    }

    public function getVotingCentersProperty()
    {
        if (!$this->parishId) {
            return collect();
        }

        return VotingCenter::where('parish_id', $this->parishId)
            ->active()
            ->orderBy('name')
            ->get();
    }

    public function getAvailableLeadersProperty()
    {
        return Person::where('is_leader_1x10', true)
            ->where('status', 'active')
            ->whereHas('assignedPeople', function ($query) {
                $query->havingRaw('COUNT(*) < 10');
            }, '<', 10)
            ->orWhere(function ($query) {
                $query->where('is_leader_1x10', true)
                    ->where('status', 'active')
                    ->doesntHave('assignedPeople');
            })
            ->orderBy('first_name')
            ->orderBy('last_name')
            ->get();
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.people.pages.create-person', [
            'states' => State::active()->orderBy('name')->get(),
            'municipalities' => $this->municipalities,
            'parishes' => $this->parishes,
            'votingCenters' => $this->votingCenters,
            'availableLeaders' => $this->availableLeaders,
        ]);
    }
}
