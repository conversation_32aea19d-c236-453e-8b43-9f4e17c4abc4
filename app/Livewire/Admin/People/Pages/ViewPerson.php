<?php

namespace App\Livewire\Admin\People\Pages;

use App\Models\Person;
use App\Models\User;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Layout;
use Livewire\Component;

class View<PERSON>erson extends Component
{
    use LivewireAlert;

    public Person $person;
    public bool $showCreateUserModal = false;

    public function mount(Person $person): void
    {
        $this->authorize('view people');
        $this->person = $person->load([
            'state',
            'municipality',
            'parish',
            'votingCenter',
            'assignedLeader',
            'assignedPeople',
            'user',
            'electoralEvents.electoralEvent',
            'mobilizations.mobilization'
        ]);
    }

    public function createUser(): void
    {
        $this->authorize('create users');

        if ($this->person->user) {
            $this->alert('error', __('people.person_already_has_user'));
            return;
        }

        if (!$this->person->email) {
            $this->alert('error', __('people.person_needs_email_for_user'));
            return;
        }

        try {
            $user = User::create([
                'name' => $this->person->full_name,
                'email' => $this->person->email,
                'username' => $this->person->document_number,
                'password' => Hash::make($this->person->document_number),
                'email_verified_at' => now(),
            ]);

            $this->person->update(['user_id' => $user->id]);
            $this->person->refresh();

            $this->alert('success', __('people.user_created_successfully'));
            $this->showCreateUserModal = false;

        } catch (\Exception $e) {
            $this->alert('error', __('people.error_creating_user'));
        }
    }

    public function toggleCreateUserModal(): void
    {
        $this->showCreateUserModal = !$this->showCreateUserModal;
    }

    public function deletePerson(): void
    {
        $this->authorize('delete people');

        // Check if has assigned people
        if ($this->person->assignedPeople()->count() > 0) {
            $this->alert('error', __('people.cannot_delete_person_with_assigned_people'));
            return;
        }

        try {
            $this->person->delete();
            $this->flash('success', __('people.person_deleted'));
            $this->redirect(route('admin.people.index'), navigate: true);

        } catch (\Exception $e) {
            $this->alert('error', __('people.error_deleting_person'));
        }
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.people.pages.view-person');
    }
}
