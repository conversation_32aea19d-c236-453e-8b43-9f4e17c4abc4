<?php

namespace Database\Seeders;

use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use App\Models\VotingCenter;
use Illuminate\Database\Seeder;

class UbicacionesSeeder extends Seeder
{
    private static $centerCounter = 1;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // All 24 Venezuelan states
        $states = [
            ['name' => 'Amazonas', 'code' => 'AM'],
            ['name' => 'Anzoátegui', 'code' => 'AN'],
            ['name' => 'Apure', 'code' => 'AP'],
            ['name' => 'Aragua', 'code' => 'AR'],
            ['name' => 'Barinas', 'code' => 'BA'],
            ['name' => 'Bolívar', 'code' => 'BO'],
            ['name' => 'Carabobo', 'code' => 'CA'],
            ['name' => 'Cojedes', 'code' => 'CO'],
            ['name' => 'Delta Amacuro', 'code' => 'DA'],
            ['name' => 'Distrito Capital', 'code' => 'DC'],
            ['name' => 'Falcón', 'code' => 'FA'],
            ['name' => 'Guárico', 'code' => 'GU'],
            ['name' => 'La Guaira', 'code' => 'LG'],
            ['name' => 'Lara', 'code' => 'LA'],
            ['name' => 'Mérida', 'code' => 'ME'],
            ['name' => 'Miranda', 'code' => 'MI'],
            ['name' => 'Monagas', 'code' => 'MO'],
            ['name' => 'Nueva Esparta', 'code' => 'NE'],
            ['name' => 'Portuguesa', 'code' => 'PO'],
            ['name' => 'Sucre', 'code' => 'SU'],
            ['name' => 'Táchira', 'code' => 'TA'],
            ['name' => 'Trujillo', 'code' => 'TR'],
            ['name' => 'Yaracuy', 'code' => 'YA'],
            ['name' => 'Zulia', 'code' => 'ZU'],
        ];

        foreach ($states as $stateData) {
            $state = State::create($stateData);

            // Create municipalities for each state
            $municipalities = $this->getMunicipalitiesByState($state->code);

            foreach ($municipalities as $municipalityData) {
                $municipality = $state->municipalities()->create($municipalityData);

                // Create parishes for each municipality
                $parishes = $this->getParishesByMunicipality($municipality->code);

                foreach ($parishes as $parishData) {
                    $parish = $municipality->parishes()->create($parishData);

                    // Create voting centers for each parish
                    $centers = $this->getCentersByParish($parish->code);

                    foreach ($centers as $centerData) {
                        $parish->votingCenters()->create($centerData);
                    }
                }
            }
        }
    }

    private function getMunicipalitiesByState(string $stateCode): array
    {
        $municipalities = [
            // Amazonas
            'AM' => [
                ['name' => 'Alto Orinoco', 'code' => 'ALO'],
                ['name' => 'Atabapo', 'code' => 'ATA'],
                ['name' => 'Atures', 'code' => 'ATU'],
                ['name' => 'Autana', 'code' => 'AUT'],
                ['name' => 'Manapiare', 'code' => 'MAN'],
                ['name' => 'Maroa', 'code' => 'MAR'],
                ['name' => 'Río Negro', 'code' => 'RNE'],
            ],
            // Anzoátegui
            'AN' => [
                ['name' => 'Anaco', 'code' => 'ANA'],
                ['name' => 'Aragua', 'code' => 'ARA'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Bruzual', 'code' => 'BRU'],
                ['name' => 'Cajigal', 'code' => 'CAJ'],
                ['name' => 'Carvajal', 'code' => 'CAR'],
                ['name' => 'Freites', 'code' => 'FRE'],
                ['name' => 'Guanipa', 'code' => 'GUA'],
                ['name' => 'Guanta', 'code' => 'GAN'],
                ['name' => 'Independencia', 'code' => 'IND'],
                ['name' => 'Libertad', 'code' => 'LIB'],
                ['name' => 'Sir Arthur McGregor', 'code' => 'SAM'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Monagas', 'code' => 'MON'],
                ['name' => 'Peñalver', 'code' => 'PEN'],
                ['name' => 'Píritu', 'code' => 'PIR'],
                ['name' => 'San Juan de Capistrano', 'code' => 'SJC'],
                ['name' => 'Santa Ana', 'code' => 'SAN'],
                ['name' => 'Simón Rodríguez', 'code' => 'SRO'],
                ['name' => 'Sotillo', 'code' => 'SOT'],
                ['name' => 'Turístico Diego Bautista Urbaneja', 'code' => 'TDB'],
            ],
            // Apure
            'AP' => [
                ['name' => 'Achaguas', 'code' => 'ACH'],
                ['name' => 'Biruaca', 'code' => 'BIR'],
                ['name' => 'Pedro Camejo', 'code' => 'PCM'],
                ['name' => 'Muñoz', 'code' => 'MUN'],
                ['name' => 'Páez', 'code' => 'PAE'],
                ['name' => 'Rómulo Gallegos', 'code' => 'RGA'],
                ['name' => 'San Fernando', 'code' => 'SFE'],
            ],
            // Aragua
            'AR' => [
                ['name' => 'Alcántara', 'code' => 'ALC'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Camatagua', 'code' => 'CAM'],
                ['name' => 'Girardot', 'code' => 'GIR'],
                ['name' => 'Iragorry', 'code' => 'IRA'],
                ['name' => 'Lamas', 'code' => 'LAM'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Mariño', 'code' => 'MAR'],
                ['name' => 'Ocumare de la Costa de Oro', 'code' => 'OCU'],
                ['name' => 'Revenga', 'code' => 'REV'],
                ['name' => 'Ribas', 'code' => 'RIB'],
                ['name' => 'San Casimiro', 'code' => 'SCA'],
                ['name' => 'San Sebastián', 'code' => 'SSE'],
                ['name' => 'Santiago Mariño', 'code' => 'SMA'],
                ['name' => 'Santos Michelena', 'code' => 'SMI'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Tovar', 'code' => 'TOV'],
                ['name' => 'Urdaneta', 'code' => 'URD'],
            ],
            // Barinas
            'BA' => [
                ['name' => 'Alberto Torrealba', 'code' => 'ATO'],
                ['name' => 'Andrés Eloy Blanco', 'code' => 'AEB'],
                ['name' => 'Antonio José de Sucre', 'code' => 'AJS'],
                ['name' => 'Arismendi', 'code' => 'ARI'],
                ['name' => 'Barinas', 'code' => 'BAR'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Cruz Paredes', 'code' => 'CPA'],
                ['name' => 'Ezequiel Zamora', 'code' => 'EZA'],
                ['name' => 'Obispos', 'code' => 'OBI'],
                ['name' => 'Pedraza', 'code' => 'PED'],
                ['name' => 'Rojas', 'code' => 'ROJ'],
                ['name' => 'Sosa', 'code' => 'SOS'],
            ],
            // Bolívar
            'BO' => [
                ['name' => 'Angostura', 'code' => 'ANG'],
                ['name' => 'Caroní', 'code' => 'CAR'],
                ['name' => 'Cedeño', 'code' => 'CED'],
                ['name' => 'El Callao', 'code' => 'ECA'],
                ['name' => 'Gran Sabana', 'code' => 'GSA'],
                ['name' => 'Heres', 'code' => 'HER'],
                ['name' => 'Piar', 'code' => 'PIA'],
                ['name' => 'Roscio', 'code' => 'ROS'],
                ['name' => 'Sifontes', 'code' => 'SIF'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Padre Pedro Chien', 'code' => 'PPC'],
            ],
            // Carabobo
            'CA' => [
                ['name' => 'Bejuma', 'code' => 'BEJ'],
                ['name' => 'Carlos Arvelo', 'code' => 'CAR'],
                ['name' => 'Diego Ibarra', 'code' => 'DIB'],
                ['name' => 'Guacara', 'code' => 'GUA'],
                ['name' => 'Juan José Mora', 'code' => 'JJM'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Los Guayos', 'code' => 'LGU'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Montalbán', 'code' => 'MON'],
                ['name' => 'Naguanagua', 'code' => 'NAG'],
                ['name' => 'Puerto Cabello', 'code' => 'PCA'],
                ['name' => 'San Diego', 'code' => 'SDI'],
                ['name' => 'San Joaquín', 'code' => 'SJO'],
                ['name' => 'Valencia', 'code' => 'VAL'],
            ],
            // Cojedes
            'CO' => [
                ['name' => 'Anzoátegui', 'code' => 'ANZ'],
                ['name' => 'Falcón', 'code' => 'FAL'],
                ['name' => 'Girardot', 'code' => 'GIR'],
                ['name' => 'Lima Blanco', 'code' => 'LBL'],
                ['name' => 'Pao de San Juan Bautista', 'code' => 'PSJ'],
                ['name' => 'Ricaurte', 'code' => 'RIC'],
                ['name' => 'Rómulo Gallegos', 'code' => 'RGA'],
                ['name' => 'San Carlos', 'code' => 'SCA'],
                ['name' => 'Tinaco', 'code' => 'TIN'],
            ],
            // Delta Amacuro
            'DA' => [
                ['name' => 'Antonio Díaz', 'code' => 'ADI'],
                ['name' => 'Casacoima', 'code' => 'CAS'],
                ['name' => 'Pedernales', 'code' => 'PED'],
                ['name' => 'Tucupita', 'code' => 'TUC'],
            ],
            // Distrito Capital
            'DC' => [
                ['name' => 'Libertador', 'code' => 'LIB'],
            ],
            // Falcón
            'FA' => [
                ['name' => 'Acosta', 'code' => 'ACO'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Buchivacoa', 'code' => 'BUC'],
                ['name' => 'Cacique Manaure', 'code' => 'CMA'],
                ['name' => 'Carirubana', 'code' => 'CAR'],
                ['name' => 'Colina', 'code' => 'COL'],
                ['name' => 'Dabajuro', 'code' => 'DAB'],
                ['name' => 'Democracia', 'code' => 'DEM'],
                ['name' => 'Falcón', 'code' => 'FAL'],
                ['name' => 'Federación', 'code' => 'FED'],
                ['name' => 'Jacura', 'code' => 'JAC'],
                ['name' => 'Los Taques', 'code' => 'LTA'],
                ['name' => 'Mauroa', 'code' => 'MAU'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Monseñor Iturriza', 'code' => 'MIT'],
                ['name' => 'Palmasola', 'code' => 'PAL'],
                ['name' => 'Petit', 'code' => 'PET'],
                ['name' => 'Píritu', 'code' => 'PIR'],
                ['name' => 'San Francisco', 'code' => 'SFR'],
                ['name' => 'Silva', 'code' => 'SIL'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Tocópero', 'code' => 'TOC'],
                ['name' => 'Unión', 'code' => 'UNI'],
                ['name' => 'Urumaco', 'code' => 'URU'],
                ['name' => 'Zamora', 'code' => 'ZAM'],
            ],
            // Guárico
            'GU' => [
                ['name' => 'Camaguán', 'code' => 'CAM'],
                ['name' => 'Chaguaramas', 'code' => 'CHA'],
                ['name' => 'El Socorro', 'code' => 'ESO'],
                ['name' => 'Francisco de Miranda', 'code' => 'FMI'],
                ['name' => 'José Félix Ribas', 'code' => 'JFR'],
                ['name' => 'José Tadeo Monagas', 'code' => 'JTM'],
                ['name' => 'Juan Germán Roscio', 'code' => 'JGR'],
                ['name' => 'Juan José Rondón', 'code' => 'JJR'],
                ['name' => 'Julián Mellado', 'code' => 'JME'],
                ['name' => 'Leonardo Infante', 'code' => 'LIN'],
                ['name' => 'Ortiz', 'code' => 'ORT'],
                ['name' => 'San Gerónimo de Guayabal', 'code' => 'SGG'],
                ['name' => 'San José de Guaribe', 'code' => 'SJG'],
                ['name' => 'Santa María de Ipire', 'code' => 'SMI'],
                ['name' => 'Zaraza', 'code' => 'ZAR'],
            ],
            // La Guaira
            'LG' => [
                ['name' => 'Vargas', 'code' => 'VAR'],
            ],
            // Lara
            'LA' => [
                ['name' => 'Andrés Eloy Blanco', 'code' => 'AEB'],
                ['name' => 'Crespo', 'code' => 'CRE'],
                ['name' => 'Iribarren', 'code' => 'IRI'],
                ['name' => 'Jiménez', 'code' => 'JIM'],
                ['name' => 'Morán', 'code' => 'MOR'],
                ['name' => 'Palavecino', 'code' => 'PAL'],
                ['name' => 'Simón Planas', 'code' => 'SPL'],
                ['name' => 'Torres', 'code' => 'TOR'],
                ['name' => 'Urdaneta', 'code' => 'URD'],
            ],
            // Mérida
            'ME' => [
                ['name' => 'Alberto Adriani', 'code' => 'AAD'],
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
                ['name' => 'Antonio Pinto Salinas', 'code' => 'APS'],
                ['name' => 'Aricagua', 'code' => 'ARI'],
                ['name' => 'Arzobispo Chacón', 'code' => 'ACH'],
                ['name' => 'Campo Elías', 'code' => 'CEL'],
                ['name' => 'Caracciolo Parra Olmedo', 'code' => 'CPO'],
                ['name' => 'Cardenal Quintero', 'code' => 'CQU'],
                ['name' => 'Guaraque', 'code' => 'GUA'],
                ['name' => 'Julio César Salas', 'code' => 'JCS'],
                ['name' => 'Justo Briceño', 'code' => 'JBR'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Obispo Ramos de Lora', 'code' => 'ORL'],
                ['name' => 'Padre Noguera', 'code' => 'PNO'],
                ['name' => 'Pueblo Llano', 'code' => 'PLL'],
                ['name' => 'Rangel', 'code' => 'RAN'],
                ['name' => 'Rivas Dávila', 'code' => 'RDA'],
                ['name' => 'Santos Marquina', 'code' => 'SMA'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Tovar', 'code' => 'TOV'],
                ['name' => 'Tulio Febres Cordero', 'code' => 'TFC'],
                ['name' => 'Zea', 'code' => 'ZEA'],
            ],
            // Miranda
            'MI' => [
                ['name' => 'Acevedo', 'code' => 'ACE'],
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
                ['name' => 'Baruta', 'code' => 'BAR'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Brión', 'code' => 'BRI'],
                ['name' => 'Buroz', 'code' => 'BUR'],
                ['name' => 'Carrizal', 'code' => 'CAR'],
                ['name' => 'Chacao', 'code' => 'CHA'],
                ['name' => 'Cristóbal Rojas', 'code' => 'CRO'],
                ['name' => 'El Hatillo', 'code' => 'HAT'],
                ['name' => 'Guaicaipuro', 'code' => 'GUA'],
                ['name' => 'Gual', 'code' => 'GUL'],
                ['name' => 'Independencia', 'code' => 'IND'],
                ['name' => 'Lander', 'code' => 'LAN'],
                ['name' => 'Los Salias', 'code' => 'LSA'],
                ['name' => 'Páez', 'code' => 'PAE'],
                ['name' => 'Paz Castillo', 'code' => 'PCA'],
                ['name' => 'Plaza', 'code' => 'PLA'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Urdaneta', 'code' => 'URD'],
                ['name' => 'Zamora', 'code' => 'ZAM'],
            ],
            // Monagas
            'MO' => [
                ['name' => 'Acosta', 'code' => 'ACO'],
                ['name' => 'Aguasay', 'code' => 'AGU'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Caripe', 'code' => 'CAR'],
                ['name' => 'Cedeño', 'code' => 'CED'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Maturín', 'code' => 'MAT'],
                ['name' => 'Piar', 'code' => 'PIA'],
                ['name' => 'Punceres', 'code' => 'PUN'],
                ['name' => 'Santa Bárbara', 'code' => 'SBA'],
                ['name' => 'Sotillo', 'code' => 'SOT'],
                ['name' => 'Uracoa', 'code' => 'URA'],
                ['name' => 'Zamora', 'code' => 'ZAM'],
            ],
            // Nueva Esparta
            'NE' => [
                ['name' => 'Antolín del Campo', 'code' => 'ADC'],
                ['name' => 'Antonio Díaz', 'code' => 'ADI'],
                ['name' => 'Arismendi', 'code' => 'ARI'],
                ['name' => 'García', 'code' => 'GAR'],
                ['name' => 'Gómez', 'code' => 'GOM'],
                ['name' => 'Macanao', 'code' => 'MAC'],
                ['name' => 'Maneiro', 'code' => 'MAN'],
                ['name' => 'Marcano', 'code' => 'MAR'],
                ['name' => 'Mariño', 'code' => 'MRI'],
                ['name' => 'Tubores', 'code' => 'TUB'],
                ['name' => 'Villalba', 'code' => 'VIL'],
            ],
            // Portuguesa
            'PO' => [
                ['name' => 'Agua Blanca', 'code' => 'ABL'],
                ['name' => 'Araure', 'code' => 'ARA'],
                ['name' => 'Esteller', 'code' => 'EST'],
                ['name' => 'Guanare', 'code' => 'GUA'],
                ['name' => 'Guanarito', 'code' => 'GAN'],
                ['name' => 'José Vicente de Unda', 'code' => 'JVU'],
                ['name' => 'Ospino', 'code' => 'OSP'],
                ['name' => 'Páez', 'code' => 'PAE'],
                ['name' => 'Papelón', 'code' => 'PAP'],
                ['name' => 'San Genaro de Boconoíto', 'code' => 'SGB'],
                ['name' => 'San Rafael de Onoto', 'code' => 'SRO'],
                ['name' => 'Santa Rosalía', 'code' => 'SRO2'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Turén', 'code' => 'TUR'],
            ],
            // Sucre
            'SU' => [
                ['name' => 'Andrés Eloy Blanco', 'code' => 'AEB'],
                ['name' => 'Andrés Mata', 'code' => 'AMA'],
                ['name' => 'Arismendi', 'code' => 'ARI'],
                ['name' => 'Benítez', 'code' => 'BEN'],
                ['name' => 'Bermúdez', 'code' => 'BER'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Cajigal', 'code' => 'CAJ'],
                ['name' => 'Cruz Salmerón Acosta', 'code' => 'CSA'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Mariño', 'code' => 'MAR'],
                ['name' => 'Mejía', 'code' => 'MEJ'],
                ['name' => 'Montes', 'code' => 'MON'],
                ['name' => 'Ribero', 'code' => 'RIB'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Valdez', 'code' => 'VAL'],
            ],
            // Táchira
            'TA' => [
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
                ['name' => 'Antonio Rómulo Costa', 'code' => 'ARC'],
                ['name' => 'Ayacucho', 'code' => 'AYA'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Cárdenas', 'code' => 'CAR'],
                ['name' => 'Córdoba', 'code' => 'COR'],
                ['name' => 'Fernández Feo', 'code' => 'FFE'],
                ['name' => 'Francisco de Miranda', 'code' => 'FMI'],
                ['name' => 'García de Hevia', 'code' => 'GHE'],
                ['name' => 'Guásimos', 'code' => 'GUA'],
                ['name' => 'Independencia', 'code' => 'IND'],
                ['name' => 'Jáuregui', 'code' => 'JAU'],
                ['name' => 'José María Vargas', 'code' => 'JMV'],
                ['name' => 'Junín', 'code' => 'JUN'],
                ['name' => 'Libertad', 'code' => 'LIB'],
                ['name' => 'Libertador', 'code' => 'LBR'],
                ['name' => 'Lobatera', 'code' => 'LOB'],
                ['name' => 'Michelena', 'code' => 'MIC'],
                ['name' => 'Panamericano', 'code' => 'PAN'],
                ['name' => 'Pedro María Ureña', 'code' => 'PMU'],
                ['name' => 'Rafael Urdaneta', 'code' => 'RUR'],
                ['name' => 'Samuel Darío Maldonado', 'code' => 'SDM'],
                ['name' => 'San Cristóbal', 'code' => 'SCR'],
                ['name' => 'San Judas Tadeo', 'code' => 'SJT'],
                ['name' => 'Seboruco', 'code' => 'SEB'],
                ['name' => 'Simón Rodríguez', 'code' => 'SRO'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Torbes', 'code' => 'TOR'],
                ['name' => 'Uribante', 'code' => 'URI'],
            ],
            // Trujillo
            'TR' => [
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
                ['name' => 'Boconó', 'code' => 'BOC'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Candelaria', 'code' => 'CAN'],
                ['name' => 'Carache', 'code' => 'CAR'],
                ['name' => 'Carvajal', 'code' => 'CAV'],
                ['name' => 'Escuque', 'code' => 'ESC'],
                ['name' => 'Juan Vicente Campo Elías', 'code' => 'JVC'],
                ['name' => 'La Ceiba', 'code' => 'LCE'],
                ['name' => 'Márquez Cañizales', 'code' => 'MCA'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Monte Carmelo', 'code' => 'MCR'],
                ['name' => 'Motatán', 'code' => 'MOT'],
                ['name' => 'Pampán', 'code' => 'PAM'],
                ['name' => 'Pampanito', 'code' => 'PAN'],
                ['name' => 'Rafael Rangel', 'code' => 'RRA'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Trujillo', 'code' => 'TRU'],
                ['name' => 'Urdaneta', 'code' => 'URD'],
                ['name' => 'Valera', 'code' => 'VAL'],
            ],
            // Yaracuy
            'YA' => [
                ['name' => 'Arístides Bastidas', 'code' => 'ABA'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Bruzual', 'code' => 'BRU'],
                ['name' => 'Cocorote', 'code' => 'COC'],
                ['name' => 'Independencia', 'code' => 'IND'],
                ['name' => 'José Antonio Páez', 'code' => 'JAP'],
                ['name' => 'La Trinidad', 'code' => 'LTR'],
                ['name' => 'Manuel Monge', 'code' => 'MMO'],
                ['name' => 'Nirgua', 'code' => 'NIR'],
                ['name' => 'Peña', 'code' => 'PEN'],
                ['name' => 'San Felipe', 'code' => 'SFE'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Urachiche', 'code' => 'URA'],
                ['name' => 'Veroes', 'code' => 'VER'],
            ],
            // Zulia
            'ZU' => [
                ['name' => 'Almirante Padilla', 'code' => 'APA'],
                ['name' => 'Baralt', 'code' => 'BAR'],
                ['name' => 'Cabimas', 'code' => 'CAB'],
                ['name' => 'Catatumbo', 'code' => 'CAT'],
                ['name' => 'Colón', 'code' => 'COL'],
                ['name' => 'Francisco Javier Pulgar', 'code' => 'FJP'],
                ['name' => 'Guajira', 'code' => 'GUA'],
                ['name' => 'Jesús Enrique Lossada', 'code' => 'JEL'],
                ['name' => 'Jesús María Semprún', 'code' => 'JMS'],
                ['name' => 'La Cañada de Urdaneta', 'code' => 'LCU'],
                ['name' => 'Lagunillas', 'code' => 'LAG'],
                ['name' => 'Machiques de Perijá', 'code' => 'MDP'],
                ['name' => 'Mara', 'code' => 'MAR'],
                ['name' => 'Maracaibo', 'code' => 'MAC'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Rosario de Perijá', 'code' => 'RDP'],
                ['name' => 'San Francisco', 'code' => 'SFR'],
                ['name' => 'Santa Rita', 'code' => 'SRI'],
                ['name' => 'Simón Bolívar', 'code' => 'SBO'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Valmore Rodríguez', 'code' => 'VRO'],
            ],
        ];

        return $municipalities[$stateCode] ?? [];
    }

    private function getParishesByMunicipality(string $municipalityCode): array
    {
        $parishes = [
            // AMAZONAS
            'ALO' => [ // Alto Orinoco
                ['name' => 'La Esmeralda', 'code' => 'LES'],
                ['name' => 'Huachamacare', 'code' => 'HUA'],
                ['name' => 'Marawaka', 'code' => 'MAR'],
                ['name' => 'Mavaca', 'code' => 'MAV'],
                ['name' => 'Sierra Parima', 'code' => 'SPA'],
            ],
            'ATA' => [ // Atabapo
                ['name' => 'Fernando Girón Tovar', 'code' => 'FGT'],
                ['name' => 'Luis Alberto Gómez', 'code' => 'LAG'],
                ['name' => 'Pahueña', 'code' => 'PAH'],
                ['name' => 'Platanillal', 'code' => 'PLA'],
            ],
            'ATU' => [ // Atures
                ['name' => 'Fernando Girón Tovar', 'code' => 'FGT2'],
                ['name' => 'Luis Alberto Gómez', 'code' => 'LAG2'],
                ['name' => 'Pahueña', 'code' => 'PAH2'],
                ['name' => 'Platanillal', 'code' => 'PLA2'],
            ],
            'AUT' => [ // Autana
                ['name' => 'Samariapo', 'code' => 'SAM'],
                ['name' => 'Sipapo', 'code' => 'SIP'],
                ['name' => 'Munduapo', 'code' => 'MUN'],
                ['name' => 'Guayapo', 'code' => 'GUA'],
            ],
            'MAN' => [ // Manapiare
                ['name' => 'Alto Ventuari', 'code' => 'AVE'],
                ['name' => 'Medio Ventuari', 'code' => 'MVE'],
                ['name' => 'Bajo Ventuari', 'code' => 'BVE'],
            ],
            'MAR' => [ // Maroa
                ['name' => 'Victorino', 'code' => 'VIC'],
                ['name' => 'Comunidad', 'code' => 'COM'],
            ],
            'RNE' => [ // Río Negro
                ['name' => 'Solano', 'code' => 'SOL'],
                ['name' => 'Casiquiare', 'code' => 'CAS'],
                ['name' => 'Cocuy', 'code' => 'COC'],
                ['name' => 'San Carlos de Río Negro', 'code' => 'SCR'],
            ],

            // DISTRITO CAPITAL
            'LIB' => [ // Libertador
                ['name' => 'Catedral', 'code' => 'CAT'],
                ['name' => 'San Juan', 'code' => 'SJU'],
                ['name' => 'Santa Teresa', 'code' => 'STE'],
                ['name' => 'La Pastora', 'code' => 'LPA'],
                ['name' => 'San José', 'code' => 'SJO'],
                ['name' => 'Altagracia', 'code' => 'ALT'],
                ['name' => 'Santa Rosalía', 'code' => 'SRO'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'San Bernardino', 'code' => 'SBE'],
                ['name' => 'El Recreo', 'code' => 'ERE'],
                ['name' => 'El Valle', 'code' => 'EVA'],
                ['name' => 'La Vega', 'code' => 'LVE'],
                ['name' => 'Macarao', 'code' => 'MAC'],
                ['name' => 'Caricuao', 'code' => 'CAR'],
                ['name' => 'Antímano', 'code' => 'ANT'],
                ['name' => 'El Junquito', 'code' => 'EJU'],
                ['name' => 'Coche', 'code' => 'COC2'],
                ['name' => 'San Agustín', 'code' => 'SAG'],
                ['name' => 'San Pedro', 'code' => 'SPE'],
                ['name' => 'Santa Juana', 'code' => 'SJA'],
                ['name' => 'El Paraíso', 'code' => 'EPA'],
                ['name' => '23 de Enero', 'code' => '23E'],
            ],

            // MIRANDA
            'ACE' => [ // Acevedo
                ['name' => 'Aragüita', 'code' => 'ARA'],
                ['name' => 'Arévalo González', 'code' => 'ARG'],
                ['name' => 'Capaya', 'code' => 'CAP'],
                ['name' => 'Caucagua', 'code' => 'CAU'],
                ['name' => 'Panaquire', 'code' => 'PAN'],
                ['name' => 'Ribas', 'code' => 'RIB'],
                ['name' => 'El Café', 'code' => 'ECA'],
                ['name' => 'Marizapa', 'code' => 'MAR'],
                ['name' => 'Yaguapa', 'code' => 'YAG'],
            ],
            'ABE' => [ // Andrés Bello
                ['name' => 'Cumbo', 'code' => 'CUM'],
                ['name' => 'San José de Barlovento', 'code' => 'SJB'],
            ],
            'BAR' => [ // Baruta
                ['name' => 'El Cafetal', 'code' => 'CAF'],
                ['name' => 'Las Minas', 'code' => 'LMI'],
                ['name' => 'Nuestra Señora del Rosario', 'code' => 'NSR'],
            ],
            'BOL' => [ // Bolívar
                ['name' => 'Mariche', 'code' => 'MAR'],
            ],
            'BRI' => [ // Brión
                ['name' => 'Higuerote', 'code' => 'HIG'],
                ['name' => 'Curiepe', 'code' => 'CUR'],
                ['name' => 'Tacarigua de Brión', 'code' => 'TAB'],
                ['name' => 'Chirimena', 'code' => 'CHI'],
                ['name' => 'Birongo', 'code' => 'BIR'],
            ],
            'BUR' => [ // Buroz
                ['name' => 'Mamporal', 'code' => 'MAM'],
            ],
            'CAR' => [ // Carrizal
                ['name' => 'Carrizal', 'code' => 'CAR'],
            ],
            'CHA' => [ // Chacao
                ['name' => 'Chacao', 'code' => 'CHA'],
            ],
            'CRO' => [ // Cristóbal Rojas
                ['name' => 'Charallave', 'code' => 'CHA'],
                ['name' => 'Las Brisas', 'code' => 'LBR'],
            ],
            'HAT' => [ // El Hatillo
                ['name' => 'Santa Rosalía de Palermo', 'code' => 'SRP'],
            ],
            'GUA' => [ // Guaicaipuro
                ['name' => 'Altagracia de la Montaña', 'code' => 'ALM'],
                ['name' => 'Cecilio Acosta', 'code' => 'CAC'],
                ['name' => 'Los Teques', 'code' => 'LTE'],
                ['name' => 'El Jarillo', 'code' => 'EJA'],
                ['name' => 'San Pedro', 'code' => 'SPE'],
                ['name' => 'Tácata', 'code' => 'TAC'],
                ['name' => 'Paracotos', 'code' => 'PAR'],
            ],
            'GUL' => [ // Gual
                ['name' => 'Cúpira', 'code' => 'CUP'],
                ['name' => 'Machurucuto', 'code' => 'MAC'],
                ['name' => 'Guarabe', 'code' => 'GUA'],
            ],
            'IND' => [ // Independencia
                ['name' => 'El Cartanal', 'code' => 'ECA'],
                ['name' => 'Santa Teresa del Tuy', 'code' => 'STT'],
            ],
            'LAN' => [ // Lander
                ['name' => 'La Democracia', 'code' => 'LDE'],
                ['name' => 'Ocumare del Tuy', 'code' => 'ODT'],
                ['name' => 'Santa Bárbara', 'code' => 'SBA'],
                ['name' => 'La Mata', 'code' => 'LMA'],
                ['name' => 'La Cabrera', 'code' => 'LCA'],
            ],
            'LSA' => [ // Los Salias
                ['name' => 'San Antonio de los Altos', 'code' => 'SAA'],
            ],
            'PAE' => [ // Páez
                ['name' => 'Río Chico', 'code' => 'RCH'],
                ['name' => 'El Guapo', 'code' => 'EGU'],
                ['name' => 'Tacarigua de la Laguna', 'code' => 'TAL'],
                ['name' => 'Paparo', 'code' => 'PAP'],
                ['name' => 'San Fernando del Guapo', 'code' => 'SFG'],
            ],
            'PCA' => [ // Paz Castillo
                ['name' => 'Santa Lucía del Tuy', 'code' => 'SLT'],
                ['name' => 'Santa Rita', 'code' => 'SRI'],
                ['name' => 'Siquire', 'code' => 'SIQ'],
                ['name' => 'Soapire', 'code' => 'SOA'],
            ],
            'PLA' => [ // Plaza
                ['name' => 'Guarenas', 'code' => 'GUA'],
            ],
            'SUC' => [ // Sucre
                ['name' => 'Leoncio Martínez', 'code' => 'LMA'],
                ['name' => 'Caucagüita', 'code' => 'CAU'],
                ['name' => 'Filas de Mariche', 'code' => 'FMA'],
                ['name' => 'La Dolorita', 'code' => 'LDO'],
                ['name' => 'Petare', 'code' => 'PET'],
            ],
            'URD' => [ // Urdaneta
                ['name' => 'Cúa', 'code' => 'CUA'],
                ['name' => 'Nueva Cúa', 'code' => 'NCU'],
            ],
            'ZAM' => [ // Zamora
                ['name' => 'Guatire', 'code' => 'GUA'],
                ['name' => 'Araira', 'code' => 'ARA'],
            ],

            // CARABOBO
            'BEJ' => [ // Bejuma
                ['name' => 'Bejuma', 'code' => 'BEJ'],
                ['name' => 'Canoabo', 'code' => 'CAN'],
                ['name' => 'Simón Bolívar', 'code' => 'SBO'],
            ],
            'CAR' => [ // Carlos Arvelo
                ['name' => 'Güigüe', 'code' => 'GUI'],
                ['name' => 'Belén', 'code' => 'BEL'],
            ],
            'DIB' => [ // Diego Ibarra
                ['name' => 'Mariara', 'code' => 'MAR'],
                ['name' => 'Aguas Calientes', 'code' => 'AGC'],
            ],
            'GUA' => [ // Guacara
                ['name' => 'Ciudad Alianza', 'code' => 'CAL'],
                ['name' => 'Guacara', 'code' => 'GUA'],
                ['name' => 'Yagua', 'code' => 'YAG'],
            ],
            'JJM' => [ // Juan José Mora
                ['name' => 'Morón', 'code' => 'MOR'],
                ['name' => 'Urama', 'code' => 'URA'],
            ],
            'LIB' => [ // Libertador
                ['name' => 'Tocuyito', 'code' => 'TOC'],
                ['name' => 'Independencia', 'code' => 'IND'],
            ],
            'LGU' => [ // Los Guayos
                ['name' => 'Los Guayos', 'code' => 'LGU'],
            ],
            'MIR' => [ // Miranda
                ['name' => 'Miranda', 'code' => 'MIR'],
            ],
            'MON' => [ // Montalbán
                ['name' => 'Montalbán', 'code' => 'MON'],
            ],
            'NAG' => [ // Naguanagua
                ['name' => 'Naguanagua', 'code' => 'NAG'],
            ],
            'PCA' => [ // Puerto Cabello
                ['name' => 'Bartolomé Salóm', 'code' => 'BSA'],
                ['name' => 'Democracia', 'code' => 'DEM'],
                ['name' => 'Fraternidad', 'code' => 'FRA'],
                ['name' => 'Goaigoaza', 'code' => 'GOA'],
                ['name' => 'Juan José Flores', 'code' => 'JJF'],
                ['name' => 'Unión', 'code' => 'UNI'],
                ['name' => 'Borburata', 'code' => 'BOR'],
                ['name' => 'Patanemo', 'code' => 'PAT'],
            ],
            'SDI' => [ // San Diego
                ['name' => 'San Diego', 'code' => 'SDI'],
            ],
            'SJO' => [ // San Joaquín
                ['name' => 'San Joaquín', 'code' => 'SJO'],
            ],
            'VAL' => [ // Valencia
                ['name' => 'Candelaria', 'code' => 'CAN'],
                ['name' => 'Catedral', 'code' => 'CAT'],
                ['name' => 'El Socorro', 'code' => 'ESO'],
                ['name' => 'Miguel Peña', 'code' => 'MPE'],
                ['name' => 'Rafael Urdaneta', 'code' => 'RUR'],
                ['name' => 'San Blas', 'code' => 'SBL'],
                ['name' => 'San José', 'code' => 'SJO'],
                ['name' => 'Santa Rosa', 'code' => 'SRO'],
                ['name' => 'Negro Primero', 'code' => 'NPR'],
            ],

            // ZULIA - Todas las parroquias de todos los municipios
            'APA' => [ // Almirante Padilla
                ['name' => 'Isla de Toas', 'code' => 'IDT'],
                ['name' => 'Monagas', 'code' => 'MON'],
            ],
            'BAR' => [ // Baralt
                ['name' => 'San Timoteo', 'code' => 'STI'],
                ['name' => 'General Urdaneta', 'code' => 'GUR'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Marcelino Briceño', 'code' => 'MBR'],
                ['name' => 'Pueblo Nuevo', 'code' => 'PNU'],
                ['name' => 'Manuel Guanipa Matos', 'code' => 'MGM'],
            ],
            'CAB' => [ // Cabimas
                ['name' => 'Ambrosio', 'code' => 'AMB'],
                ['name' => 'Arístides Calvani', 'code' => 'ACA'],
                ['name' => 'Carmen Herrera', 'code' => 'CHE'],
                ['name' => 'Germán Ríos Linares', 'code' => 'GRL'],
                ['name' => 'Jorge Hernández', 'code' => 'JHE'],
                ['name' => 'La Rosa', 'code' => 'LRO'],
                ['name' => 'Punta Gorda', 'code' => 'PGO'],
                ['name' => 'Rómulo Betancourt', 'code' => 'RBE'],
                ['name' => 'San Benito', 'code' => 'SBE'],
            ],
            'CAT' => [ // Catatumbo
                ['name' => 'Encontrados', 'code' => 'ENC'],
                ['name' => 'Udón Pérez', 'code' => 'UPE'],
            ],
            'COL' => [ // Colón
                ['name' => 'San Carlos del Zulia', 'code' => 'SCZ'],
                ['name' => 'Moralito', 'code' => 'MOR'],
                ['name' => 'Santa Bárbara', 'code' => 'SBA'],
                ['name' => 'Santa Cruz del Zulia', 'code' => 'SCR'],
                ['name' => 'Urribarrí', 'code' => 'URR'],
            ],
            'FJP' => [ // Francisco Javier Pulgar
                ['name' => 'Simón Rodríguez', 'code' => 'SRO'],
                ['name' => 'Agustín Codazzi', 'code' => 'ACO'],
                ['name' => 'Carlos Quevedo', 'code' => 'CQU'],
                ['name' => 'Francisco Javier Pulgar', 'code' => 'FJP'],
            ],
            'GUA' => [ // Guajira
                ['name' => 'Sinamaica', 'code' => 'SIN'],
                ['name' => 'Alta Guajira', 'code' => 'AGU'],
                ['name' => 'Elías Sánchez Rubio', 'code' => 'ESR'],
                ['name' => 'Guajira', 'code' => 'GUA'],
            ],
            'JEL' => [ // Jesús Enrique Lossada
                ['name' => 'La Concepción', 'code' => 'LCO'],
                ['name' => 'San José', 'code' => 'SJO'],
                ['name' => 'Mariano Parra León', 'code' => 'MPL'],
                ['name' => 'José Ramón Yépez', 'code' => 'JRY'],
            ],
            'JMS' => [ // Jesús María Semprún
                ['name' => 'Jesús María Semprún', 'code' => 'JMS'],
                ['name' => 'Barí', 'code' => 'BAR'],
            ],
            'LCU' => [ // La Cañada de Urdaneta
                ['name' => 'Concepción', 'code' => 'CON'],
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
                ['name' => 'Chiquinquirá', 'code' => 'CHI'],
                ['name' => 'El Carmelo', 'code' => 'ECA'],
                ['name' => 'Potreritos', 'code' => 'POT'],
            ],
            'LAG' => [ // Lagunillas
                ['name' => 'Alonso de Ojeda', 'code' => 'ADO'],
                ['name' => 'Libertad', 'code' => 'LIB'],
                ['name' => 'Eleazar López Contreras', 'code' => 'ELC'],
                ['name' => 'Campo Lara', 'code' => 'CLA'],
                ['name' => 'Venezuela', 'code' => 'VEN'],
                ['name' => 'El Danto', 'code' => 'EDA'],
            ],
            'MDP' => [ // Machiques de Perijá
                ['name' => 'Libertad', 'code' => 'LIB2'],
                ['name' => 'Bartolomé de las Casas', 'code' => 'BCA'],
                ['name' => 'Río Negro', 'code' => 'RNE'],
                ['name' => 'San José de Perijá', 'code' => 'SJP'],
            ],
            'MAR' => [ // Mara
                ['name' => 'San Rafael', 'code' => 'SRA'],
                ['name' => 'La Sierrita', 'code' => 'LSI'],
                ['name' => 'Las Parcelas', 'code' => 'LPA'],
                ['name' => 'Luis De Vicente', 'code' => 'LDV'],
                ['name' => 'Monseñor Marcos Sergio Godoy', 'code' => 'MSG'],
                ['name' => 'Ricaurte', 'code' => 'RIC'],
                ['name' => 'Tamare', 'code' => 'TAM'],
            ],
            'MAC' => [ // Maracaibo
                ['name' => 'Antonio Borjas Romero', 'code' => 'ABR'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Cacique Mara', 'code' => 'CMA'],
                ['name' => 'Carracciolo Parra Pérez', 'code' => 'CPP'],
                ['name' => 'Cecilio Acosta', 'code' => 'CAC'],
                ['name' => 'Chiquinquirá', 'code' => 'CHI2'],
                ['name' => 'Coquivacoa', 'code' => 'COQ'],
                ['name' => 'Cristo de Aranza', 'code' => 'CDA'],
                ['name' => 'Francisco Eugenio Bustamante', 'code' => 'FEB'],
                ['name' => 'Idelfonzo Vásquez', 'code' => 'IVA'],
                ['name' => 'Juana de Ávila', 'code' => 'JDA'],
                ['name' => 'Luis Hurtado Higuera', 'code' => 'LHH'],
                ['name' => 'Manuel Dagnino', 'code' => 'MDA'],
                ['name' => 'Olegario Villalobos', 'code' => 'OVI'],
                ['name' => 'Raúl Leoni', 'code' => 'RLE'],
                ['name' => 'San Isidro', 'code' => 'SIS'],
                ['name' => 'Santa Lucía', 'code' => 'SLU'],
                ['name' => 'Venancio Pulgar', 'code' => 'VPU'],
            ],
            'MIR' => [ // Miranda
                ['name' => 'Altagracia', 'code' => 'ALT'],
                ['name' => 'Ana María Campos', 'code' => 'AMC'],
                ['name' => 'Faría', 'code' => 'FAR'],
                ['name' => 'San Antonio', 'code' => 'SAN'],
                ['name' => 'San José', 'code' => 'SJO2'],
            ],
            'RDP' => [ // Rosario de Perijá
                ['name' => 'El Rosario', 'code' => 'ERO'],
                ['name' => 'Donaldo García', 'code' => 'DGA'],
                ['name' => 'Sixto Zambrano', 'code' => 'SZA'],
            ],
            'SFR' => [ // San Francisco
                ['name' => 'San Francisco', 'code' => 'SFR'],
                ['name' => 'El Bajo', 'code' => 'EBA'],
                ['name' => 'Domitila Flores', 'code' => 'DFL'],
                ['name' => 'Francisco Ochoa', 'code' => 'FOC'],
                ['name' => 'Los Cortijos', 'code' => 'LCO'],
                ['name' => 'Marcial Hernández', 'code' => 'MHE'],
                ['name' => 'José Domingo Rus', 'code' => 'JDR'],
            ],
            'SRI' => [ // Santa Rita
                ['name' => 'Santa Rita', 'code' => 'SRI'],
                ['name' => 'El Mene', 'code' => 'EME'],
                ['name' => 'José Cenobio Urribarrí', 'code' => 'JCU'],
                ['name' => 'Pedro Lucas Urribarrí', 'code' => 'PLU'],
            ],
            'SBO' => [ // Simón Bolívar
                ['name' => 'Manuel Manrique', 'code' => 'MMA'],
                ['name' => 'Rafael Maria Baralt', 'code' => 'RMB'],
                ['name' => 'Rafael Urdaneta', 'code' => 'RUR'],
            ],
            'SUC' => [ // Sucre
                ['name' => 'Bobures', 'code' => 'BOB'],
                ['name' => 'El Batey', 'code' => 'EBA2'],
                ['name' => 'Gibraltar', 'code' => 'GIB'],
                ['name' => 'Heras', 'code' => 'HER'],
                ['name' => 'Monseñor Arturo Álvarez', 'code' => 'MAA'],
                ['name' => 'Rómulo Gallegos', 'code' => 'RGA'],
            ],
            'VRO' => [ // Valmore Rodríguez
                ['name' => 'Rafael Urdaneta', 'code' => 'RUR2'],
                ['name' => 'La Victoria', 'code' => 'LVI'],
                ['name' => 'Raúl Cuenca', 'code' => 'RCU'],
            ],

            // ARAGUA - Municipio Girardot
            'GIR' => [ // Girardot
                ['name' => 'Maracay', 'code' => 'MAR'],
                ['name' => 'San Jacinto', 'code' => 'SJA'],
                ['name' => 'Madre María de San José', 'code' => 'MMS'],
                ['name' => 'Andrés Eloy Blanco', 'code' => 'AEB'],
                ['name' => 'Los Tacarigua', 'code' => 'LTA'],
                ['name' => 'Las Delicias', 'code' => 'LDE'],
                ['name' => 'Choroní', 'code' => 'CHO'],
            ],

            // ANZOÁTEGUI - Municipio Bolívar
            'BOL' => [ // Bolívar (Barcelona)
                ['name' => 'Barcelona', 'code' => 'BAR'],
                ['name' => 'El Carmen', 'code' => 'ECA'],
                ['name' => 'San Cristóbal', 'code' => 'SCR'],
                ['name' => 'Bergantín', 'code' => 'BER'],
                ['name' => 'Caigua', 'code' => 'CAI'],
                ['name' => 'El Pilar', 'code' => 'EPI'],
                ['name' => 'Naricual', 'code' => 'NAR'],
            ],

            // LARA - Municipio Iribarren
            'IRI' => [ // Iribarren (Barquisimeto)
                ['name' => 'Catedral', 'code' => 'CAT'],
                ['name' => 'Concepción', 'code' => 'CON'],
                ['name' => 'El Cují', 'code' => 'ECU'],
                ['name' => 'Juan de Villegas', 'code' => 'JVL'],
                ['name' => 'Santa Rosa', 'code' => 'SRO'],
                ['name' => 'Tamaca', 'code' => 'TAM'],
                ['name' => 'Unión', 'code' => 'UNI'],
                ['name' => 'Aguedo Felipe Alvarado', 'code' => 'AFA'],
                ['name' => 'Buena Vista', 'code' => 'BVI'],
                ['name' => 'Juárez', 'code' => 'JUA'],
            ],

            // TÁCHIRA - Municipio San Cristóbal
            'SCR' => [ // San Cristóbal
                ['name' => 'La Concordia', 'code' => 'LCO'],
                ['name' => 'Pedro María Morantes', 'code' => 'PMM'],
                ['name' => 'San Juan Bautista', 'code' => 'SJB'],
                ['name' => 'San Sebastián', 'code' => 'SSE'],
                ['name' => 'Dr. Francisco Romero Lobo', 'code' => 'FRL'],
            ],

            // MÉRIDA - Municipio Libertador
            'LBR' => [ // Libertador (Mérida)
                ['name' => 'Arias', 'code' => 'ARI'],
                ['name' => 'Sagrario', 'code' => 'SAG'],
                ['name' => 'Milla', 'code' => 'MIL'],
                ['name' => 'El Llano', 'code' => 'ELL'],
                ['name' => 'Juan Rodríguez Suárez', 'code' => 'JRS'],
                ['name' => 'Jacinto Plaza', 'code' => 'JPL'],
                ['name' => 'Domingo Peña', 'code' => 'DPE'],
                ['name' => 'Gonzalo Picón Febres', 'code' => 'GPF'],
                ['name' => 'Osuna Rodríguez', 'code' => 'ORO'],
                ['name' => 'Lasso de la Vega', 'code' => 'LLV'],
                ['name' => 'Caracciolo Parra Pérez', 'code' => 'CPP'],
                ['name' => 'Mariano Picón Salas', 'code' => 'MPS'],
                ['name' => 'Antonio Spinetti Dini', 'code' => 'ASD'],
                ['name' => 'El Morro', 'code' => 'EMO'],
            ],

            // FALCÓN - Municipio Carirubana
            'CAR' => [ // Carirubana (Punto Fijo)
                ['name' => 'Norte', 'code' => 'NOR'],
                ['name' => 'Carirubana', 'code' => 'CAR'],
                ['name' => 'Santa Ana', 'code' => 'SAN'],
                ['name' => 'Urbana Punta Cardón', 'code' => 'UPC'],
            ],

            // NUEVA ESPARTA - Municipio Mariño
            'MRI' => [ // Mariño (Porlamar)
                ['name' => 'Porlamar', 'code' => 'POR'],
            ],

            // BOLÍVAR - Municipio Heres
            'HER' => [ // Heres (Ciudad Bolívar)
                ['name' => 'Catedral', 'code' => 'CAT'],
                ['name' => 'Zea', 'code' => 'ZEA'],
                ['name' => 'Orinoco', 'code' => 'ORI'],
                ['name' => 'José Antonio Páez', 'code' => 'JAP'],
                ['name' => 'Marhuanta', 'code' => 'MAR'],
                ['name' => 'Agua Salada', 'code' => 'ASA'],
                ['name' => 'Vista Hermosa', 'code' => 'VHE'],
                ['name' => 'La Sabanita', 'code' => 'LSA'],
                ['name' => 'Panapana', 'code' => 'PAN'],
            ],

            // MONAGAS - Municipio Maturín
            'MAT' => [ // Maturín
                ['name' => 'Alto de los Godos', 'code' => 'ADG'],
                ['name' => 'Boquerón', 'code' => 'BOQ'],
                ['name' => 'Las Cocuizas', 'code' => 'LCO'],
                ['name' => 'San Simón', 'code' => 'SSI'],
                ['name' => 'Santa Cruz', 'code' => 'SCR'],
                ['name' => 'San Vicente', 'code' => 'SVI'],
            ],

            // PORTUGUESA - Municipio Guanare
            'GUA' => [ // Guanare
                ['name' => 'Guanare', 'code' => 'GUA'],
                ['name' => 'Córdoba', 'code' => 'COR'],
                ['name' => 'San José de la Montaña', 'code' => 'SJM'],
                ['name' => 'San Juan de Guanaguanare', 'code' => 'SJG'],
                ['name' => 'Virgen de la Coromoto', 'code' => 'VCO'],
            ],

            // BARINAS - Municipio Barinas
            'BAR' => [ // Barinas
                ['name' => 'Barinas', 'code' => 'BAR'],
                ['name' => 'Alberto Torrealba', 'code' => 'ATO'],
                ['name' => 'San Silvestre', 'code' => 'SSI'],
                ['name' => 'Santa Inés', 'code' => 'SIN'],
                ['name' => 'Santa Lucía', 'code' => 'SLU'],
                ['name' => 'Torunos', 'code' => 'TOR'],
                ['name' => 'El Carmen', 'code' => 'ECA'],
                ['name' => 'Rómulo Betancourt', 'code' => 'RBE'],
                ['name' => 'Corazón de Jesús', 'code' => 'CDJ'],
                ['name' => 'Ramón Ignacio Méndez', 'code' => 'RIM'],
                ['name' => 'Alto Barinas', 'code' => 'ABA'],
                ['name' => 'Manuel Palacio Fajardo', 'code' => 'MPF'],
                ['name' => 'Juan Antonio Rodríguez Domínguez', 'code' => 'JAR'],
                ['name' => 'Dominga Ortiz de Páez', 'code' => 'DOP'],
            ],
        ];

        return $parishes[$municipalityCode] ?? [];
    }

    private function getCentersByParish(string $parishCode): array
    {
        // Generate unique codes using a global counter to avoid duplicates
        $uniqueId1 = 'VC_' . str_pad(self::$centerCounter++, 6, '0', STR_PAD_LEFT);
        $uniqueId2 = 'VC_' . str_pad(self::$centerCounter++, 6, '0', STR_PAD_LEFT);

        return [
            [
                'name' => 'Basic School ' . $parishCode . ' 001',
                'code' => $uniqueId1,
                'address' => 'Example address for center ' . $parishCode . ' 001',
            ],
            [
                'name' => 'High School ' . $parishCode . ' 002',
                'code' => $uniqueId2,
                'address' => 'Example address for center ' . $parishCode . ' 002',
            ],
        ];
    }
}
