<?php

namespace Database\Seeders;

use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use App\Models\VotingCenter;
use Illuminate\Database\Seeder;

class UbicacionesSeeder extends Seeder
{
    private static $centerCounter = 1;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // All 24 Venezuelan states
        $states = [
            ['name' => 'Amazonas', 'code' => 'AM'],
            ['name' => 'Anzoátegui', 'code' => 'AN'],
            ['name' => 'Apure', 'code' => 'AP'],
            ['name' => 'Aragua', 'code' => 'AR'],
            ['name' => 'Barinas', 'code' => 'BA'],
            ['name' => 'Bolívar', 'code' => 'BO'],
            ['name' => 'Carabobo', 'code' => 'CA'],
            ['name' => 'Cojedes', 'code' => 'CO'],
            ['name' => 'Delta Amacuro', 'code' => 'DA'],
            ['name' => 'Distrito Capital', 'code' => 'DC'],
            ['name' => 'Falcón', 'code' => 'FA'],
            ['name' => 'Guárico', 'code' => 'GU'],
            ['name' => 'La Guaira', 'code' => 'LG'],
            ['name' => 'Lara', 'code' => 'LA'],
            ['name' => 'Mérida', 'code' => 'ME'],
            ['name' => 'Miranda', 'code' => 'MI'],
            ['name' => 'Monagas', 'code' => 'MO'],
            ['name' => 'Nueva Esparta', 'code' => 'NE'],
            ['name' => 'Portuguesa', 'code' => 'PO'],
            ['name' => 'Sucre', 'code' => 'SU'],
            ['name' => 'Táchira', 'code' => 'TA'],
            ['name' => 'Trujillo', 'code' => 'TR'],
            ['name' => 'Yaracuy', 'code' => 'YA'],
            ['name' => 'Zulia', 'code' => 'ZU'],
        ];

        foreach ($states as $stateData) {
            $state = State::create($stateData);

            // Create municipalities for each state
            $municipalities = $this->getMunicipalitiesByState($state->code);

            foreach ($municipalities as $municipalityData) {
                $municipality = $state->municipalities()->create($municipalityData);

                // Create parishes for each municipality
                $parishes = $this->getParishesByMunicipality($municipality->code);

                foreach ($parishes as $parishData) {
                    $parish = $municipality->parishes()->create($parishData);

                    // Create voting centers for each parish
                    $centers = $this->getCentersByParish($parish->code);

                    foreach ($centers as $centerData) {
                        $parish->votingCenters()->create($centerData);
                    }
                }
            }
        }
    }

    private function getMunicipalitiesByState(string $stateCode): array
    {
        $municipalities = [
            // Amazonas
            'AM' => [
                ['name' => 'Alto Orinoco', 'code' => 'ALO'],
                ['name' => 'Atabapo', 'code' => 'ATA'],
                ['name' => 'Atures', 'code' => 'ATU'],
                ['name' => 'Autana', 'code' => 'AUT'],
                ['name' => 'Manapiare', 'code' => 'MAN'],
                ['name' => 'Maroa', 'code' => 'MAR'],
                ['name' => 'Río Negro', 'code' => 'RNE'],
            ],
            // Anzoátegui
            'AN' => [
                ['name' => 'Anaco', 'code' => 'ANA'],
                ['name' => 'Aragua', 'code' => 'ARA'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Bruzual', 'code' => 'BRU'],
                ['name' => 'Cajigal', 'code' => 'CAJ'],
                ['name' => 'Carvajal', 'code' => 'CAR'],
                ['name' => 'Freites', 'code' => 'FRE'],
                ['name' => 'Guanipa', 'code' => 'GUA'],
                ['name' => 'Guanta', 'code' => 'GAN'],
                ['name' => 'Independencia', 'code' => 'IND'],
                ['name' => 'Libertad', 'code' => 'LIB'],
                ['name' => 'Sir Arthur McGregor', 'code' => 'SAM'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Monagas', 'code' => 'MON'],
                ['name' => 'Peñalver', 'code' => 'PEN'],
                ['name' => 'Píritu', 'code' => 'PIR'],
                ['name' => 'San Juan de Capistrano', 'code' => 'SJC'],
                ['name' => 'Santa Ana', 'code' => 'SAN'],
                ['name' => 'Simón Rodríguez', 'code' => 'SRO'],
                ['name' => 'Sotillo', 'code' => 'SOT'],
                ['name' => 'Turístico Diego Bautista Urbaneja', 'code' => 'TDB'],
            ],
            // Apure
            'AP' => [
                ['name' => 'Achaguas', 'code' => 'ACH'],
                ['name' => 'Biruaca', 'code' => 'BIR'],
                ['name' => 'Pedro Camejo', 'code' => 'PCM'],
                ['name' => 'Muñoz', 'code' => 'MUN'],
                ['name' => 'Páez', 'code' => 'PAE'],
                ['name' => 'Rómulo Gallegos', 'code' => 'RGA'],
                ['name' => 'San Fernando', 'code' => 'SFE'],
            ],
            // Aragua
            'AR' => [
                ['name' => 'Alcántara', 'code' => 'ALC'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Camatagua', 'code' => 'CAM'],
                ['name' => 'Girardot', 'code' => 'GIR'],
                ['name' => 'Iragorry', 'code' => 'IRA'],
                ['name' => 'Lamas', 'code' => 'LAM'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Mariño', 'code' => 'MAR'],
                ['name' => 'Ocumare de la Costa de Oro', 'code' => 'OCU'],
                ['name' => 'Revenga', 'code' => 'REV'],
                ['name' => 'Ribas', 'code' => 'RIB'],
                ['name' => 'San Casimiro', 'code' => 'SCA'],
                ['name' => 'San Sebastián', 'code' => 'SSE'],
                ['name' => 'Santiago Mariño', 'code' => 'SMA'],
                ['name' => 'Santos Michelena', 'code' => 'SMI'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Tovar', 'code' => 'TOV'],
                ['name' => 'Urdaneta', 'code' => 'URD'],
            ],
            // Barinas
            'BA' => [
                ['name' => 'Alberto Torrealba', 'code' => 'ATO'],
                ['name' => 'Andrés Eloy Blanco', 'code' => 'AEB'],
                ['name' => 'Antonio José de Sucre', 'code' => 'AJS'],
                ['name' => 'Arismendi', 'code' => 'ARI'],
                ['name' => 'Barinas', 'code' => 'BAR'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Cruz Paredes', 'code' => 'CPA'],
                ['name' => 'Ezequiel Zamora', 'code' => 'EZA'],
                ['name' => 'Obispos', 'code' => 'OBI'],
                ['name' => 'Pedraza', 'code' => 'PED'],
                ['name' => 'Rojas', 'code' => 'ROJ'],
                ['name' => 'Sosa', 'code' => 'SOS'],
            ],
            // Bolívar
            'BO' => [
                ['name' => 'Angostura', 'code' => 'ANG'],
                ['name' => 'Caroní', 'code' => 'CAR'],
                ['name' => 'Cedeño', 'code' => 'CED'],
                ['name' => 'El Callao', 'code' => 'ECA'],
                ['name' => 'Gran Sabana', 'code' => 'GSA'],
                ['name' => 'Heres', 'code' => 'HER'],
                ['name' => 'Piar', 'code' => 'PIA'],
                ['name' => 'Roscio', 'code' => 'ROS'],
                ['name' => 'Sifontes', 'code' => 'SIF'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Padre Pedro Chien', 'code' => 'PPC'],
            ],
            // Carabobo
            'CA' => [
                ['name' => 'Bejuma', 'code' => 'BEJ'],
                ['name' => 'Carlos Arvelo', 'code' => 'CAR'],
                ['name' => 'Diego Ibarra', 'code' => 'DIB'],
                ['name' => 'Guacara', 'code' => 'GUA'],
                ['name' => 'Juan José Mora', 'code' => 'JJM'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Los Guayos', 'code' => 'LGU'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Montalbán', 'code' => 'MON'],
                ['name' => 'Naguanagua', 'code' => 'NAG'],
                ['name' => 'Puerto Cabello', 'code' => 'PCA'],
                ['name' => 'San Diego', 'code' => 'SDI'],
                ['name' => 'San Joaquín', 'code' => 'SJO'],
                ['name' => 'Valencia', 'code' => 'VAL'],
            ],
            // Cojedes
            'CO' => [
                ['name' => 'Anzoátegui', 'code' => 'ANZ'],
                ['name' => 'Falcón', 'code' => 'FAL'],
                ['name' => 'Girardot', 'code' => 'GIR'],
                ['name' => 'Lima Blanco', 'code' => 'LBL'],
                ['name' => 'Pao de San Juan Bautista', 'code' => 'PSJ'],
                ['name' => 'Ricaurte', 'code' => 'RIC'],
                ['name' => 'Rómulo Gallegos', 'code' => 'RGA'],
                ['name' => 'San Carlos', 'code' => 'SCA'],
                ['name' => 'Tinaco', 'code' => 'TIN'],
            ],
            // Delta Amacuro
            'DA' => [
                ['name' => 'Antonio Díaz', 'code' => 'ADI'],
                ['name' => 'Casacoima', 'code' => 'CAS'],
                ['name' => 'Pedernales', 'code' => 'PED'],
                ['name' => 'Tucupita', 'code' => 'TUC'],
            ],
            // Distrito Capital
            'DC' => [
                ['name' => 'Libertador', 'code' => 'LIB'],
            ],
            // Falcón
            'FA' => [
                ['name' => 'Acosta', 'code' => 'ACO'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Buchivacoa', 'code' => 'BUC'],
                ['name' => 'Cacique Manaure', 'code' => 'CMA'],
                ['name' => 'Carirubana', 'code' => 'CAR'],
                ['name' => 'Colina', 'code' => 'COL'],
                ['name' => 'Dabajuro', 'code' => 'DAB'],
                ['name' => 'Democracia', 'code' => 'DEM'],
                ['name' => 'Falcón', 'code' => 'FAL'],
                ['name' => 'Federación', 'code' => 'FED'],
                ['name' => 'Jacura', 'code' => 'JAC'],
                ['name' => 'Los Taques', 'code' => 'LTA'],
                ['name' => 'Mauroa', 'code' => 'MAU'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Monseñor Iturriza', 'code' => 'MIT'],
                ['name' => 'Palmasola', 'code' => 'PAL'],
                ['name' => 'Petit', 'code' => 'PET'],
                ['name' => 'Píritu', 'code' => 'PIR'],
                ['name' => 'San Francisco', 'code' => 'SFR'],
                ['name' => 'Silva', 'code' => 'SIL'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Tocópero', 'code' => 'TOC'],
                ['name' => 'Unión', 'code' => 'UNI'],
                ['name' => 'Urumaco', 'code' => 'URU'],
                ['name' => 'Zamora', 'code' => 'ZAM'],
            ],
            // Guárico
            'GU' => [
                ['name' => 'Camaguán', 'code' => 'CAM'],
                ['name' => 'Chaguaramas', 'code' => 'CHA'],
                ['name' => 'El Socorro', 'code' => 'ESO'],
                ['name' => 'Francisco de Miranda', 'code' => 'FMI'],
                ['name' => 'José Félix Ribas', 'code' => 'JFR'],
                ['name' => 'José Tadeo Monagas', 'code' => 'JTM'],
                ['name' => 'Juan Germán Roscio', 'code' => 'JGR'],
                ['name' => 'Juan José Rondón', 'code' => 'JJR'],
                ['name' => 'Julián Mellado', 'code' => 'JME'],
                ['name' => 'Leonardo Infante', 'code' => 'LIN'],
                ['name' => 'Ortiz', 'code' => 'ORT'],
                ['name' => 'San Gerónimo de Guayabal', 'code' => 'SGG'],
                ['name' => 'San José de Guaribe', 'code' => 'SJG'],
                ['name' => 'Santa María de Ipire', 'code' => 'SMI'],
                ['name' => 'Zaraza', 'code' => 'ZAR'],
            ],
            // La Guaira
            'LG' => [
                ['name' => 'Vargas', 'code' => 'VAR'],
            ],
            // Lara
            'LA' => [
                ['name' => 'Andrés Eloy Blanco', 'code' => 'AEB'],
                ['name' => 'Crespo', 'code' => 'CRE'],
                ['name' => 'Iribarren', 'code' => 'IRI'],
                ['name' => 'Jiménez', 'code' => 'JIM'],
                ['name' => 'Morán', 'code' => 'MOR'],
                ['name' => 'Palavecino', 'code' => 'PAL'],
                ['name' => 'Simón Planas', 'code' => 'SPL'],
                ['name' => 'Torres', 'code' => 'TOR'],
                ['name' => 'Urdaneta', 'code' => 'URD'],
            ],
            // Mérida
            'ME' => [
                ['name' => 'Alberto Adriani', 'code' => 'AAD'],
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
                ['name' => 'Antonio Pinto Salinas', 'code' => 'APS'],
                ['name' => 'Aricagua', 'code' => 'ARI'],
                ['name' => 'Arzobispo Chacón', 'code' => 'ACH'],
                ['name' => 'Campo Elías', 'code' => 'CEL'],
                ['name' => 'Caracciolo Parra Olmedo', 'code' => 'CPO'],
                ['name' => 'Cardenal Quintero', 'code' => 'CQU'],
                ['name' => 'Guaraque', 'code' => 'GUA'],
                ['name' => 'Julio César Salas', 'code' => 'JCS'],
                ['name' => 'Justo Briceño', 'code' => 'JBR'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Obispo Ramos de Lora', 'code' => 'ORL'],
                ['name' => 'Padre Noguera', 'code' => 'PNO'],
                ['name' => 'Pueblo Llano', 'code' => 'PLL'],
                ['name' => 'Rangel', 'code' => 'RAN'],
                ['name' => 'Rivas Dávila', 'code' => 'RDA'],
                ['name' => 'Santos Marquina', 'code' => 'SMA'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Tovar', 'code' => 'TOV'],
                ['name' => 'Tulio Febres Cordero', 'code' => 'TFC'],
                ['name' => 'Zea', 'code' => 'ZEA'],
            ],
            // Miranda
            'MI' => [
                ['name' => 'Acevedo', 'code' => 'ACE'],
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
                ['name' => 'Baruta', 'code' => 'BAR'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Brión', 'code' => 'BRI'],
                ['name' => 'Buroz', 'code' => 'BUR'],
                ['name' => 'Carrizal', 'code' => 'CAR'],
                ['name' => 'Chacao', 'code' => 'CHA'],
                ['name' => 'Cristóbal Rojas', 'code' => 'CRO'],
                ['name' => 'El Hatillo', 'code' => 'HAT'],
                ['name' => 'Guaicaipuro', 'code' => 'GUA'],
                ['name' => 'Gual', 'code' => 'GUL'],
                ['name' => 'Independencia', 'code' => 'IND'],
                ['name' => 'Lander', 'code' => 'LAN'],
                ['name' => 'Los Salias', 'code' => 'LSA'],
                ['name' => 'Páez', 'code' => 'PAE'],
                ['name' => 'Paz Castillo', 'code' => 'PCA'],
                ['name' => 'Plaza', 'code' => 'PLA'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Urdaneta', 'code' => 'URD'],
                ['name' => 'Zamora', 'code' => 'ZAM'],
            ],
            // Monagas
            'MO' => [
                ['name' => 'Acosta', 'code' => 'ACO'],
                ['name' => 'Aguasay', 'code' => 'AGU'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Caripe', 'code' => 'CAR'],
                ['name' => 'Cedeño', 'code' => 'CED'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Maturín', 'code' => 'MAT'],
                ['name' => 'Piar', 'code' => 'PIA'],
                ['name' => 'Punceres', 'code' => 'PUN'],
                ['name' => 'Santa Bárbara', 'code' => 'SBA'],
                ['name' => 'Sotillo', 'code' => 'SOT'],
                ['name' => 'Uracoa', 'code' => 'URA'],
                ['name' => 'Zamora', 'code' => 'ZAM'],
            ],
            // Nueva Esparta
            'NE' => [
                ['name' => 'Antolín del Campo', 'code' => 'ADC'],
                ['name' => 'Antonio Díaz', 'code' => 'ADI'],
                ['name' => 'Arismendi', 'code' => 'ARI'],
                ['name' => 'García', 'code' => 'GAR'],
                ['name' => 'Gómez', 'code' => 'GOM'],
                ['name' => 'Macanao', 'code' => 'MAC'],
                ['name' => 'Maneiro', 'code' => 'MAN'],
                ['name' => 'Marcano', 'code' => 'MAR'],
                ['name' => 'Mariño', 'code' => 'MRI'],
                ['name' => 'Tubores', 'code' => 'TUB'],
                ['name' => 'Villalba', 'code' => 'VIL'],
            ],
            // Portuguesa
            'PO' => [
                ['name' => 'Agua Blanca', 'code' => 'ABL'],
                ['name' => 'Araure', 'code' => 'ARA'],
                ['name' => 'Esteller', 'code' => 'EST'],
                ['name' => 'Guanare', 'code' => 'GUA'],
                ['name' => 'Guanarito', 'code' => 'GAN'],
                ['name' => 'José Vicente de Unda', 'code' => 'JVU'],
                ['name' => 'Ospino', 'code' => 'OSP'],
                ['name' => 'Páez', 'code' => 'PAE'],
                ['name' => 'Papelón', 'code' => 'PAP'],
                ['name' => 'San Genaro de Boconoíto', 'code' => 'SGB'],
                ['name' => 'San Rafael de Onoto', 'code' => 'SRO'],
                ['name' => 'Santa Rosalía', 'code' => 'SRO2'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Turén', 'code' => 'TUR'],
            ],
            // Sucre
            'SU' => [
                ['name' => 'Andrés Eloy Blanco', 'code' => 'AEB'],
                ['name' => 'Andrés Mata', 'code' => 'AMA'],
                ['name' => 'Arismendi', 'code' => 'ARI'],
                ['name' => 'Benítez', 'code' => 'BEN'],
                ['name' => 'Bermúdez', 'code' => 'BER'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Cajigal', 'code' => 'CAJ'],
                ['name' => 'Cruz Salmerón Acosta', 'code' => 'CSA'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Mariño', 'code' => 'MAR'],
                ['name' => 'Mejía', 'code' => 'MEJ'],
                ['name' => 'Montes', 'code' => 'MON'],
                ['name' => 'Ribero', 'code' => 'RIB'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Valdez', 'code' => 'VAL'],
            ],
            // Táchira
            'TA' => [
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
                ['name' => 'Antonio Rómulo Costa', 'code' => 'ARC'],
                ['name' => 'Ayacucho', 'code' => 'AYA'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Cárdenas', 'code' => 'CAR'],
                ['name' => 'Córdoba', 'code' => 'COR'],
                ['name' => 'Fernández Feo', 'code' => 'FFE'],
                ['name' => 'Francisco de Miranda', 'code' => 'FMI'],
                ['name' => 'García de Hevia', 'code' => 'GHE'],
                ['name' => 'Guásimos', 'code' => 'GUA'],
                ['name' => 'Independencia', 'code' => 'IND'],
                ['name' => 'Jáuregui', 'code' => 'JAU'],
                ['name' => 'José María Vargas', 'code' => 'JMV'],
                ['name' => 'Junín', 'code' => 'JUN'],
                ['name' => 'Libertad', 'code' => 'LIB'],
                ['name' => 'Libertador', 'code' => 'LBR'],
                ['name' => 'Lobatera', 'code' => 'LOB'],
                ['name' => 'Michelena', 'code' => 'MIC'],
                ['name' => 'Panamericano', 'code' => 'PAN'],
                ['name' => 'Pedro María Ureña', 'code' => 'PMU'],
                ['name' => 'Rafael Urdaneta', 'code' => 'RUR'],
                ['name' => 'Samuel Darío Maldonado', 'code' => 'SDM'],
                ['name' => 'San Cristóbal', 'code' => 'SCR'],
                ['name' => 'San Judas Tadeo', 'code' => 'SJT'],
                ['name' => 'Seboruco', 'code' => 'SEB'],
                ['name' => 'Simón Rodríguez', 'code' => 'SRO'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Torbes', 'code' => 'TOR'],
                ['name' => 'Uribante', 'code' => 'URI'],
            ],
            // Trujillo
            'TR' => [
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
                ['name' => 'Boconó', 'code' => 'BOC'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Candelaria', 'code' => 'CAN'],
                ['name' => 'Carache', 'code' => 'CAR'],
                ['name' => 'Carvajal', 'code' => 'CAV'],
                ['name' => 'Escuque', 'code' => 'ESC'],
                ['name' => 'Juan Vicente Campo Elías', 'code' => 'JVC'],
                ['name' => 'La Ceiba', 'code' => 'LCE'],
                ['name' => 'Márquez Cañizales', 'code' => 'MCA'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Monte Carmelo', 'code' => 'MCR'],
                ['name' => 'Motatán', 'code' => 'MOT'],
                ['name' => 'Pampán', 'code' => 'PAM'],
                ['name' => 'Pampanito', 'code' => 'PAN'],
                ['name' => 'Rafael Rangel', 'code' => 'RRA'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Trujillo', 'code' => 'TRU'],
                ['name' => 'Urdaneta', 'code' => 'URD'],
                ['name' => 'Valera', 'code' => 'VAL'],
            ],
            // Yaracuy
            'YA' => [
                ['name' => 'Arístides Bastidas', 'code' => 'ABA'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Bruzual', 'code' => 'BRU'],
                ['name' => 'Cocorote', 'code' => 'COC'],
                ['name' => 'Independencia', 'code' => 'IND'],
                ['name' => 'José Antonio Páez', 'code' => 'JAP'],
                ['name' => 'La Trinidad', 'code' => 'LTR'],
                ['name' => 'Manuel Monge', 'code' => 'MMO'],
                ['name' => 'Nirgua', 'code' => 'NIR'],
                ['name' => 'Peña', 'code' => 'PEN'],
                ['name' => 'San Felipe', 'code' => 'SFE'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Urachiche', 'code' => 'URA'],
                ['name' => 'Veroes', 'code' => 'VER'],
            ],
            // Zulia
            'ZU' => [
                ['name' => 'Almirante Padilla', 'code' => 'APA'],
                ['name' => 'Baralt', 'code' => 'BAR'],
                ['name' => 'Cabimas', 'code' => 'CAB'],
                ['name' => 'Catatumbo', 'code' => 'CAT'],
                ['name' => 'Colón', 'code' => 'COL'],
                ['name' => 'Francisco Javier Pulgar', 'code' => 'FJP'],
                ['name' => 'Guajira', 'code' => 'GUA'],
                ['name' => 'Jesús Enrique Lossada', 'code' => 'JEL'],
                ['name' => 'Jesús María Semprún', 'code' => 'JMS'],
                ['name' => 'La Cañada de Urdaneta', 'code' => 'LCU'],
                ['name' => 'Lagunillas', 'code' => 'LAG'],
                ['name' => 'Machiques de Perijá', 'code' => 'MDP'],
                ['name' => 'Mara', 'code' => 'MAR'],
                ['name' => 'Maracaibo', 'code' => 'MAC'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Rosario de Perijá', 'code' => 'RDP'],
                ['name' => 'San Francisco', 'code' => 'SFR'],
                ['name' => 'Santa Rita', 'code' => 'SRI'],
                ['name' => 'Simón Bolívar', 'code' => 'SBO'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Valmore Rodríguez', 'code' => 'VRO'],
            ],
        ];

        return $municipalities[$stateCode] ?? [];
    }

    private function getParishesByMunicipality(string $municipalityCode): array
    {
        $parishes = [
            'LIB' => [
                ['name' => 'Catedral', 'code' => 'CAT'],
                ['name' => 'San Juan', 'code' => 'SJU'],
                ['name' => 'Santa Teresa', 'code' => 'STE'],
            ],
            'BAR' => [
                ['name' => 'Baruta', 'code' => 'BAR'],
                ['name' => 'El Cafetal', 'code' => 'CAF'],
            ],
            'CHA' => [
                ['name' => 'Chacao', 'code' => 'CHA'],
            ],
            'HAT' => [
                ['name' => 'El Hatillo', 'code' => 'HAT'],
            ],
            'SUC' => [
                ['name' => 'Petare', 'code' => 'PET'],
                ['name' => 'Caucagüita', 'code' => 'CAU'],
            ],
            'VAL' => [
                ['name' => 'Valencia', 'code' => 'VAL'],
                ['name' => 'Miguel Peña', 'code' => 'MPE'],
            ],
            'PCA' => [
                ['name' => 'Puerto Cabello', 'code' => 'PCA'],
            ],
            'MAR' => [
                ['name' => 'Maracaibo', 'code' => 'MAR'],
                ['name' => 'Cacique Mara', 'code' => 'CMA'],
            ],
            'SFR' => [
                ['name' => 'San Francisco', 'code' => 'SFR'],
            ],
            'GIR' => [
                ['name' => 'Maracay', 'code' => 'MAC'],
                ['name' => 'San Jacinto', 'code' => 'SJA'],
            ],
        ];

        return $parishes[$municipalityCode] ?? [];
    }

    private function getCentersByParish(string $parishCode): array
    {
        // Generate unique codes using a global counter to avoid duplicates
        $uniqueId1 = 'VC_' . str_pad(self::$centerCounter++, 6, '0', STR_PAD_LEFT);
        $uniqueId2 = 'VC_' . str_pad(self::$centerCounter++, 6, '0', STR_PAD_LEFT);

        return [
            [
                'name' => 'Basic School ' . $parishCode . ' 001',
                'code' => $uniqueId1,
                'address' => 'Example address for center ' . $parishCode . ' 001',
            ],
            [
                'name' => 'High School ' . $parishCode . ' 002',
                'code' => $uniqueId2,
                'address' => 'Example address for center ' . $parishCode . ' 002',
            ],
        ];
    }
}
