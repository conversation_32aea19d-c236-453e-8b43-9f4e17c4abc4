<?php

namespace Database\Seeders;

use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use App\Models\VotingCenter;
use Illuminate\Database\Seeder;

class UbicacionesSeeder extends Seeder
{
    private static $centerCounter = 1;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // All 24 Venezuelan states
        $states = [
            ['name' => 'Amazonas', 'code' => 'AM'],
            ['name' => 'Anzoátegui', 'code' => 'AN'],
            ['name' => 'Apure', 'code' => 'AP'],
            ['name' => 'Aragua', 'code' => 'AR'],
            ['name' => 'Barinas', 'code' => 'BA'],
            ['name' => 'Bolívar', 'code' => 'BO'],
            ['name' => 'Carabobo', 'code' => 'CA'],
            ['name' => 'Cojedes', 'code' => 'CO'],
            ['name' => 'Delta Amacuro', 'code' => 'DA'],
            ['name' => 'Distrito Capital', 'code' => 'DC'],
            ['name' => 'Falcón', 'code' => 'FA'],
            ['name' => 'Guárico', 'code' => 'GU'],
            ['name' => 'La Guaira', 'code' => 'LG'],
            ['name' => 'Lara', 'code' => 'LA'],
            ['name' => 'Mérida', 'code' => 'ME'],
            ['name' => 'Miranda', 'code' => 'MI'],
            ['name' => 'Monagas', 'code' => 'MO'],
            ['name' => 'Nueva Esparta', 'code' => 'NE'],
            ['name' => 'Portuguesa', 'code' => 'PO'],
            ['name' => 'Sucre', 'code' => 'SU'],
            ['name' => 'Táchira', 'code' => 'TA'],
            ['name' => 'Trujillo', 'code' => 'TR'],
            ['name' => 'Yaracuy', 'code' => 'YA'],
            ['name' => 'Zulia', 'code' => 'ZU'],
        ];

        foreach ($states as $stateData) {
            $state = State::create($stateData);

            // Create municipalities for each state
            $municipalities = $this->getMunicipalitiesByState($state->code);

            foreach ($municipalities as $municipalityData) {
                $municipality = $state->municipalities()->create($municipalityData);

                // Create parishes for each municipality
                $parishes = $this->getParishesByMunicipality($municipality->code);

                foreach ($parishes as $parishData) {
                    $parish = $municipality->parishes()->create($parishData);

                    // Create voting centers for each parish
                    $centers = $this->getCentersByParish($parish->code);

                    foreach ($centers as $centerData) {
                        $parish->votingCenters()->create($centerData);
                    }
                }
            }
        }
    }

    private function getMunicipalitiesByState(string $stateCode): array
    {
        $municipalities = [
            // Amazonas
            'AM' => [
                ['name' => 'Alto Orinoco', 'code' => 'ALO'],
                ['name' => 'Atabapo', 'code' => 'ATA'],
                ['name' => 'Atures', 'code' => 'ATU'],
                ['name' => 'Autana', 'code' => 'AUT'],
                ['name' => 'Manapiare', 'code' => 'MAN'],
                ['name' => 'Maroa', 'code' => 'MAR'],
                ['name' => 'Río Negro', 'code' => 'RNE'],
            ],
            // Anzoátegui
            'AN' => [
                ['name' => 'Anaco', 'code' => 'ANA'],
                ['name' => 'Aragua', 'code' => 'ARA'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Bruzual', 'code' => 'BRU'],
                ['name' => 'Cajigal', 'code' => 'CAJ'],
                ['name' => 'Carvajal', 'code' => 'CAR'],
                ['name' => 'Freites', 'code' => 'FRE'],
                ['name' => 'Guanipa', 'code' => 'GUA'],
                ['name' => 'Guanta', 'code' => 'GAN'],
                ['name' => 'Independencia', 'code' => 'IND'],
                ['name' => 'Libertad', 'code' => 'LIB'],
                ['name' => 'Sir Arthur McGregor', 'code' => 'SAM'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Monagas', 'code' => 'MON'],
                ['name' => 'Peñalver', 'code' => 'PEN'],
                ['name' => 'Píritu', 'code' => 'PIR'],
                ['name' => 'San Juan de Capistrano', 'code' => 'SJC'],
                ['name' => 'Santa Ana', 'code' => 'SAN'],
                ['name' => 'Simón Rodríguez', 'code' => 'SRO'],
                ['name' => 'Sotillo', 'code' => 'SOT'],
                ['name' => 'Turístico Diego Bautista Urbaneja', 'code' => 'TDB'],
            ],
            // Apure
            'AP' => [
                ['name' => 'Achaguas', 'code' => 'ACH'],
                ['name' => 'Biruaca', 'code' => 'BIR'],
                ['name' => 'Pedro Camejo', 'code' => 'PCM'],
                ['name' => 'Muñoz', 'code' => 'MUN'],
                ['name' => 'Páez', 'code' => 'PAE'],
                ['name' => 'Rómulo Gallegos', 'code' => 'RGA'],
                ['name' => 'San Fernando', 'code' => 'SFE'],
            ],
            // Aragua
            'AR' => [
                ['name' => 'Alcántara', 'code' => 'ALC'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Camatagua', 'code' => 'CAM'],
                ['name' => 'Girardot', 'code' => 'GIR'],
                ['name' => 'Iragorry', 'code' => 'IRA'],
                ['name' => 'Lamas', 'code' => 'LAM'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Mariño', 'code' => 'MAR'],
                ['name' => 'Ocumare de la Costa de Oro', 'code' => 'OCU'],
                ['name' => 'Revenga', 'code' => 'REV'],
                ['name' => 'Ribas', 'code' => 'RIB'],
                ['name' => 'San Casimiro', 'code' => 'SCA'],
                ['name' => 'San Sebastián', 'code' => 'SSE'],
                ['name' => 'Santiago Mariño', 'code' => 'SMA'],
                ['name' => 'Santos Michelena', 'code' => 'SMI'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Tovar', 'code' => 'TOV'],
                ['name' => 'Urdaneta', 'code' => 'URD'],
            ],
            // Barinas
            'BA' => [
                ['name' => 'Alberto Torrealba', 'code' => 'ATO'],
                ['name' => 'Andrés Eloy Blanco', 'code' => 'AEB'],
                ['name' => 'Antonio José de Sucre', 'code' => 'AJS'],
                ['name' => 'Arismendi', 'code' => 'ARI'],
                ['name' => 'Barinas', 'code' => 'BAR'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Cruz Paredes', 'code' => 'CPA'],
                ['name' => 'Ezequiel Zamora', 'code' => 'EZA'],
                ['name' => 'Obispos', 'code' => 'OBI'],
                ['name' => 'Pedraza', 'code' => 'PED'],
                ['name' => 'Rojas', 'code' => 'ROJ'],
                ['name' => 'Sosa', 'code' => 'SOS'],
            ],
            // Bolívar
            'BO' => [
                ['name' => 'Angostura', 'code' => 'ANG'],
                ['name' => 'Caroní', 'code' => 'CAR'],
                ['name' => 'Cedeño', 'code' => 'CED'],
                ['name' => 'El Callao', 'code' => 'ECA'],
                ['name' => 'Gran Sabana', 'code' => 'GSA'],
                ['name' => 'Heres', 'code' => 'HER'],
                ['name' => 'Piar', 'code' => 'PIA'],
                ['name' => 'Roscio', 'code' => 'ROS'],
                ['name' => 'Sifontes', 'code' => 'SIF'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Padre Pedro Chien', 'code' => 'PPC'],
            ],
            // Carabobo
            'CA' => [
                ['name' => 'Bejuma', 'code' => 'BEJ'],
                ['name' => 'Carlos Arvelo', 'code' => 'CAR'],
                ['name' => 'Diego Ibarra', 'code' => 'DIB'],
                ['name' => 'Guacara', 'code' => 'GUA'],
                ['name' => 'Juan José Mora', 'code' => 'JJM'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Los Guayos', 'code' => 'LGU'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Montalbán', 'code' => 'MON'],
                ['name' => 'Naguanagua', 'code' => 'NAG'],
                ['name' => 'Puerto Cabello', 'code' => 'PCA'],
                ['name' => 'San Diego', 'code' => 'SDI'],
                ['name' => 'San Joaquín', 'code' => 'SJO'],
                ['name' => 'Valencia', 'code' => 'VAL'],
            ],
            // Cojedes
            'CO' => [
                ['name' => 'Anzoátegui', 'code' => 'ANZ'],
                ['name' => 'Falcón', 'code' => 'FAL'],
                ['name' => 'Girardot', 'code' => 'GIR'],
                ['name' => 'Lima Blanco', 'code' => 'LBL'],
                ['name' => 'Pao de San Juan Bautista', 'code' => 'PSJ'],
                ['name' => 'Ricaurte', 'code' => 'RIC'],
                ['name' => 'Rómulo Gallegos', 'code' => 'RGA'],
                ['name' => 'San Carlos', 'code' => 'SCA'],
                ['name' => 'Tinaco', 'code' => 'TIN'],
            ],
            // Delta Amacuro
            'DA' => [
                ['name' => 'Antonio Díaz', 'code' => 'ADI'],
                ['name' => 'Casacoima', 'code' => 'CAS'],
                ['name' => 'Pedernales', 'code' => 'PED'],
                ['name' => 'Tucupita', 'code' => 'TUC'],
            ],
            // Distrito Capital
            'DC' => [
                ['name' => 'Libertador', 'code' => 'LIB'],
            ],
            // Falcón
            'FA' => [
                ['name' => 'Acosta', 'code' => 'ACO'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Buchivacoa', 'code' => 'BUC'],
                ['name' => 'Cacique Manaure', 'code' => 'CMA'],
                ['name' => 'Carirubana', 'code' => 'CAR'],
                ['name' => 'Colina', 'code' => 'COL'],
                ['name' => 'Dabajuro', 'code' => 'DAB'],
                ['name' => 'Democracia', 'code' => 'DEM'],
                ['name' => 'Falcón', 'code' => 'FAL'],
                ['name' => 'Federación', 'code' => 'FED'],
                ['name' => 'Jacura', 'code' => 'JAC'],
                ['name' => 'Los Taques', 'code' => 'LTA'],
                ['name' => 'Mauroa', 'code' => 'MAU'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Monseñor Iturriza', 'code' => 'MIT'],
                ['name' => 'Palmasola', 'code' => 'PAL'],
                ['name' => 'Petit', 'code' => 'PET'],
                ['name' => 'Píritu', 'code' => 'PIR'],
                ['name' => 'San Francisco', 'code' => 'SFR'],
                ['name' => 'Silva', 'code' => 'SIL'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Tocópero', 'code' => 'TOC'],
                ['name' => 'Unión', 'code' => 'UNI'],
                ['name' => 'Urumaco', 'code' => 'URU'],
                ['name' => 'Zamora', 'code' => 'ZAM'],
            ],
            // Guárico
            'GU' => [
                ['name' => 'Camaguán', 'code' => 'CAM'],
                ['name' => 'Chaguaramas', 'code' => 'CHA'],
                ['name' => 'El Socorro', 'code' => 'ESO'],
                ['name' => 'Francisco de Miranda', 'code' => 'FMI'],
                ['name' => 'José Félix Ribas', 'code' => 'JFR'],
                ['name' => 'José Tadeo Monagas', 'code' => 'JTM'],
                ['name' => 'Juan Germán Roscio', 'code' => 'JGR'],
                ['name' => 'Juan José Rondón', 'code' => 'JJR'],
                ['name' => 'Julián Mellado', 'code' => 'JME'],
                ['name' => 'Leonardo Infante', 'code' => 'LIN'],
                ['name' => 'Ortiz', 'code' => 'ORT'],
                ['name' => 'San Gerónimo de Guayabal', 'code' => 'SGG'],
                ['name' => 'San José de Guaribe', 'code' => 'SJG'],
                ['name' => 'Santa María de Ipire', 'code' => 'SMI'],
                ['name' => 'Zaraza', 'code' => 'ZAR'],
            ],
            // La Guaira
            'LG' => [
                ['name' => 'Vargas', 'code' => 'VAR'],
            ],
            // Lara
            'LA' => [
                ['name' => 'Andrés Eloy Blanco', 'code' => 'AEB'],
                ['name' => 'Crespo', 'code' => 'CRE'],
                ['name' => 'Iribarren', 'code' => 'IRI'],
                ['name' => 'Jiménez', 'code' => 'JIM'],
                ['name' => 'Morán', 'code' => 'MOR'],
                ['name' => 'Palavecino', 'code' => 'PAL'],
                ['name' => 'Simón Planas', 'code' => 'SPL'],
                ['name' => 'Torres', 'code' => 'TOR'],
                ['name' => 'Urdaneta', 'code' => 'URD'],
            ],
            // Mérida
            'ME' => [
                ['name' => 'Alberto Adriani', 'code' => 'AAD'],
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
                ['name' => 'Antonio Pinto Salinas', 'code' => 'APS'],
                ['name' => 'Aricagua', 'code' => 'ARI'],
                ['name' => 'Arzobispo Chacón', 'code' => 'ACH'],
                ['name' => 'Campo Elías', 'code' => 'CEL'],
                ['name' => 'Caracciolo Parra Olmedo', 'code' => 'CPO'],
                ['name' => 'Cardenal Quintero', 'code' => 'CQU'],
                ['name' => 'Guaraque', 'code' => 'GUA'],
                ['name' => 'Julio César Salas', 'code' => 'JCS'],
                ['name' => 'Justo Briceño', 'code' => 'JBR'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Obispo Ramos de Lora', 'code' => 'ORL'],
                ['name' => 'Padre Noguera', 'code' => 'PNO'],
                ['name' => 'Pueblo Llano', 'code' => 'PLL'],
                ['name' => 'Rangel', 'code' => 'RAN'],
                ['name' => 'Rivas Dávila', 'code' => 'RDA'],
                ['name' => 'Santos Marquina', 'code' => 'SMA'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Tovar', 'code' => 'TOV'],
                ['name' => 'Tulio Febres Cordero', 'code' => 'TFC'],
                ['name' => 'Zea', 'code' => 'ZEA'],
            ],
            // Miranda
            'MI' => [
                ['name' => 'Acevedo', 'code' => 'ACE'],
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
                ['name' => 'Baruta', 'code' => 'BAR'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Brión', 'code' => 'BRI'],
                ['name' => 'Buroz', 'code' => 'BUR'],
                ['name' => 'Carrizal', 'code' => 'CAR'],
                ['name' => 'Chacao', 'code' => 'CHA'],
                ['name' => 'Cristóbal Rojas', 'code' => 'CRO'],
                ['name' => 'El Hatillo', 'code' => 'HAT'],
                ['name' => 'Guaicaipuro', 'code' => 'GUA'],
                ['name' => 'Gual', 'code' => 'GUL'],
                ['name' => 'Independencia', 'code' => 'IND'],
                ['name' => 'Lander', 'code' => 'LAN'],
                ['name' => 'Los Salias', 'code' => 'LSA'],
                ['name' => 'Páez', 'code' => 'PAE'],
                ['name' => 'Paz Castillo', 'code' => 'PCA'],
                ['name' => 'Plaza', 'code' => 'PLA'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Urdaneta', 'code' => 'URD'],
                ['name' => 'Zamora', 'code' => 'ZAM'],
            ],
            // Monagas
            'MO' => [
                ['name' => 'Acosta', 'code' => 'ACO'],
                ['name' => 'Aguasay', 'code' => 'AGU'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Caripe', 'code' => 'CAR'],
                ['name' => 'Cedeño', 'code' => 'CED'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Maturín', 'code' => 'MAT'],
                ['name' => 'Piar', 'code' => 'PIA'],
                ['name' => 'Punceres', 'code' => 'PUN'],
                ['name' => 'Santa Bárbara', 'code' => 'SBA'],
                ['name' => 'Sotillo', 'code' => 'SOT'],
                ['name' => 'Uracoa', 'code' => 'URA'],
                ['name' => 'Zamora', 'code' => 'ZAM'],
            ],
            // Nueva Esparta
            'NE' => [
                ['name' => 'Antolín del Campo', 'code' => 'ADC'],
                ['name' => 'Antonio Díaz', 'code' => 'ADI'],
                ['name' => 'Arismendi', 'code' => 'ARI'],
                ['name' => 'García', 'code' => 'GAR'],
                ['name' => 'Gómez', 'code' => 'GOM'],
                ['name' => 'Macanao', 'code' => 'MAC'],
                ['name' => 'Maneiro', 'code' => 'MAN'],
                ['name' => 'Marcano', 'code' => 'MAR'],
                ['name' => 'Mariño', 'code' => 'MRI'],
                ['name' => 'Tubores', 'code' => 'TUB'],
                ['name' => 'Villalba', 'code' => 'VIL'],
            ],
            // Portuguesa
            'PO' => [
                ['name' => 'Agua Blanca', 'code' => 'ABL'],
                ['name' => 'Araure', 'code' => 'ARA'],
                ['name' => 'Esteller', 'code' => 'EST'],
                ['name' => 'Guanare', 'code' => 'GUA'],
                ['name' => 'Guanarito', 'code' => 'GAN'],
                ['name' => 'José Vicente de Unda', 'code' => 'JVU'],
                ['name' => 'Ospino', 'code' => 'OSP'],
                ['name' => 'Páez', 'code' => 'PAE'],
                ['name' => 'Papelón', 'code' => 'PAP'],
                ['name' => 'San Genaro de Boconoíto', 'code' => 'SGB'],
                ['name' => 'San Rafael de Onoto', 'code' => 'SRO'],
                ['name' => 'Santa Rosalía', 'code' => 'SRO2'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Turén', 'code' => 'TUR'],
            ],
            // Sucre
            'SU' => [
                ['name' => 'Andrés Eloy Blanco', 'code' => 'AEB'],
                ['name' => 'Andrés Mata', 'code' => 'AMA'],
                ['name' => 'Arismendi', 'code' => 'ARI'],
                ['name' => 'Benítez', 'code' => 'BEN'],
                ['name' => 'Bermúdez', 'code' => 'BER'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Cajigal', 'code' => 'CAJ'],
                ['name' => 'Cruz Salmerón Acosta', 'code' => 'CSA'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Mariño', 'code' => 'MAR'],
                ['name' => 'Mejía', 'code' => 'MEJ'],
                ['name' => 'Montes', 'code' => 'MON'],
                ['name' => 'Ribero', 'code' => 'RIB'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Valdez', 'code' => 'VAL'],
            ],
            // Táchira
            'TA' => [
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
                ['name' => 'Antonio Rómulo Costa', 'code' => 'ARC'],
                ['name' => 'Ayacucho', 'code' => 'AYA'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Cárdenas', 'code' => 'CAR'],
                ['name' => 'Córdoba', 'code' => 'COR'],
                ['name' => 'Fernández Feo', 'code' => 'FFE'],
                ['name' => 'Francisco de Miranda', 'code' => 'FMI'],
                ['name' => 'García de Hevia', 'code' => 'GHE'],
                ['name' => 'Guásimos', 'code' => 'GUA'],
                ['name' => 'Independencia', 'code' => 'IND'],
                ['name' => 'Jáuregui', 'code' => 'JAU'],
                ['name' => 'José María Vargas', 'code' => 'JMV'],
                ['name' => 'Junín', 'code' => 'JUN'],
                ['name' => 'Libertad', 'code' => 'LIB'],
                ['name' => 'Libertador', 'code' => 'LBR'],
                ['name' => 'Lobatera', 'code' => 'LOB'],
                ['name' => 'Michelena', 'code' => 'MIC'],
                ['name' => 'Panamericano', 'code' => 'PAN'],
                ['name' => 'Pedro María Ureña', 'code' => 'PMU'],
                ['name' => 'Rafael Urdaneta', 'code' => 'RUR'],
                ['name' => 'Samuel Darío Maldonado', 'code' => 'SDM'],
                ['name' => 'San Cristóbal', 'code' => 'SCR'],
                ['name' => 'San Judas Tadeo', 'code' => 'SJT'],
                ['name' => 'Seboruco', 'code' => 'SEB'],
                ['name' => 'Simón Rodríguez', 'code' => 'SRO'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Torbes', 'code' => 'TOR'],
                ['name' => 'Uribante', 'code' => 'URI'],
            ],
            // Trujillo
            'TR' => [
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
                ['name' => 'Boconó', 'code' => 'BOC'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Candelaria', 'code' => 'CAN'],
                ['name' => 'Carache', 'code' => 'CAR'],
                ['name' => 'Carvajal', 'code' => 'CAV'],
                ['name' => 'Escuque', 'code' => 'ESC'],
                ['name' => 'Juan Vicente Campo Elías', 'code' => 'JVC'],
                ['name' => 'La Ceiba', 'code' => 'LCE'],
                ['name' => 'Márquez Cañizales', 'code' => 'MCA'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Monte Carmelo', 'code' => 'MCR'],
                ['name' => 'Motatán', 'code' => 'MOT'],
                ['name' => 'Pampán', 'code' => 'PAM'],
                ['name' => 'Pampanito', 'code' => 'PAN'],
                ['name' => 'Rafael Rangel', 'code' => 'RRA'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Trujillo', 'code' => 'TRU'],
                ['name' => 'Urdaneta', 'code' => 'URD'],
                ['name' => 'Valera', 'code' => 'VAL'],
            ],
            // Yaracuy
            'YA' => [
                ['name' => 'Arístides Bastidas', 'code' => 'ABA'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Bruzual', 'code' => 'BRU'],
                ['name' => 'Cocorote', 'code' => 'COC'],
                ['name' => 'Independencia', 'code' => 'IND'],
                ['name' => 'José Antonio Páez', 'code' => 'JAP'],
                ['name' => 'La Trinidad', 'code' => 'LTR'],
                ['name' => 'Manuel Monge', 'code' => 'MMO'],
                ['name' => 'Nirgua', 'code' => 'NIR'],
                ['name' => 'Peña', 'code' => 'PEN'],
                ['name' => 'San Felipe', 'code' => 'SFE'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Urachiche', 'code' => 'URA'],
                ['name' => 'Veroes', 'code' => 'VER'],
            ],
            // Zulia
            'ZU' => [
                ['name' => 'Almirante Padilla', 'code' => 'APA'],
                ['name' => 'Baralt', 'code' => 'BAR'],
                ['name' => 'Cabimas', 'code' => 'CAB'],
                ['name' => 'Catatumbo', 'code' => 'CAT'],
                ['name' => 'Colón', 'code' => 'COL'],
                ['name' => 'Francisco Javier Pulgar', 'code' => 'FJP'],
                ['name' => 'Guajira', 'code' => 'GUA'],
                ['name' => 'Jesús Enrique Lossada', 'code' => 'JEL'],
                ['name' => 'Jesús María Semprún', 'code' => 'JMS'],
                ['name' => 'La Cañada de Urdaneta', 'code' => 'LCU'],
                ['name' => 'Lagunillas', 'code' => 'LAG'],
                ['name' => 'Machiques de Perijá', 'code' => 'MDP'],
                ['name' => 'Mara', 'code' => 'MAR'],
                ['name' => 'Maracaibo', 'code' => 'MAC'],
                ['name' => 'Miranda', 'code' => 'MIR'],
                ['name' => 'Rosario de Perijá', 'code' => 'RDP'],
                ['name' => 'San Francisco', 'code' => 'SFR'],
                ['name' => 'Santa Rita', 'code' => 'SRI'],
                ['name' => 'Simón Bolívar', 'code' => 'SBO'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'Valmore Rodríguez', 'code' => 'VRO'],
            ],
        ];

        return $municipalities[$stateCode] ?? [];
    }

    private function getParishesByMunicipality(string $municipalityCode): array
    {
        $parishes = [
            // AMAZONAS
            'ALO' => [ // Alto Orinoco
                ['name' => 'La Esmeralda', 'code' => 'LES'],
                ['name' => 'Huachamacare', 'code' => 'HUA'],
                ['name' => 'Marawaka', 'code' => 'MAR'],
                ['name' => 'Mavaca', 'code' => 'MAV'],
                ['name' => 'Sierra Parima', 'code' => 'SPA'],
            ],
            'ATA' => [ // Atabapo
                ['name' => 'Fernando Girón Tovar', 'code' => 'FGT'],
                ['name' => 'Luis Alberto Gómez', 'code' => 'LAG'],
                ['name' => 'Pahueña', 'code' => 'PAH'],
                ['name' => 'Platanillal', 'code' => 'PLA'],
            ],
            'ATU' => [ // Atures
                ['name' => 'Fernando Girón Tovar', 'code' => 'FGT2'],
                ['name' => 'Luis Alberto Gómez', 'code' => 'LAG2'],
                ['name' => 'Pahueña', 'code' => 'PAH2'],
                ['name' => 'Platanillal', 'code' => 'PLA2'],
            ],
            'AUT' => [ // Autana
                ['name' => 'Samariapo', 'code' => 'SAM'],
                ['name' => 'Sipapo', 'code' => 'SIP'],
                ['name' => 'Munduapo', 'code' => 'MUN'],
                ['name' => 'Guayapo', 'code' => 'GUA'],
            ],
            'MAN' => [ // Manapiare
                ['name' => 'Alto Ventuari', 'code' => 'AVE'],
                ['name' => 'Medio Ventuari', 'code' => 'MVE'],
                ['name' => 'Bajo Ventuari', 'code' => 'BVE'],
            ],
            'MAR' => [ // Maroa
                ['name' => 'Victorino', 'code' => 'VIC'],
                ['name' => 'Comunidad', 'code' => 'COM'],
            ],
            'RNE' => [ // Río Negro
                ['name' => 'Solano', 'code' => 'SOL'],
                ['name' => 'Casiquiare', 'code' => 'CAS'],
                ['name' => 'Cocuy', 'code' => 'COC'],
                ['name' => 'San Carlos de Río Negro', 'code' => 'SCR'],
            ],

            // DISTRITO CAPITAL
            'LIB' => [ // Libertador
                ['name' => 'Catedral', 'code' => 'CAT'],
                ['name' => 'San Juan', 'code' => 'SJU'],
                ['name' => 'Santa Teresa', 'code' => 'STE'],
                ['name' => 'La Pastora', 'code' => 'LPA'],
                ['name' => 'San José', 'code' => 'SJO'],
                ['name' => 'Altagracia', 'code' => 'ALT'],
                ['name' => 'Santa Rosalía', 'code' => 'SRO'],
                ['name' => 'Sucre', 'code' => 'SUC'],
                ['name' => 'San Bernardino', 'code' => 'SBE'],
                ['name' => 'El Recreo', 'code' => 'ERE'],
                ['name' => 'El Valle', 'code' => 'EVA'],
                ['name' => 'La Vega', 'code' => 'LVE'],
                ['name' => 'Macarao', 'code' => 'MAC'],
                ['name' => 'Caricuao', 'code' => 'CAR'],
                ['name' => 'Antímano', 'code' => 'ANT'],
                ['name' => 'El Junquito', 'code' => 'EJU'],
                ['name' => 'Coche', 'code' => 'COC2'],
                ['name' => 'San Agustín', 'code' => 'SAG'],
                ['name' => 'San Pedro', 'code' => 'SPE'],
                ['name' => 'Santa Juana', 'code' => 'SJA'],
                ['name' => 'El Paraíso', 'code' => 'EPA'],
                ['name' => '23 de Enero', 'code' => '23E'],
            ],

            // MIRANDA
            'ACE' => [ // Acevedo
                ['name' => 'Aragüita', 'code' => 'ARA'],
                ['name' => 'Arévalo González', 'code' => 'ARG'],
                ['name' => 'Capaya', 'code' => 'CAP'],
                ['name' => 'Caucagua', 'code' => 'CAU'],
                ['name' => 'Panaquire', 'code' => 'PAN'],
                ['name' => 'Ribas', 'code' => 'RIB'],
                ['name' => 'El Café', 'code' => 'ECA'],
                ['name' => 'Marizapa', 'code' => 'MAR'],
                ['name' => 'Yaguapa', 'code' => 'YAG'],
            ],
            'ABE' => [ // Andrés Bello
                ['name' => 'Cumbo', 'code' => 'CUM'],
                ['name' => 'San José de Barlovento', 'code' => 'SJB'],
            ],
            'BAR' => [ // Baruta
                ['name' => 'El Cafetal', 'code' => 'CAF'],
                ['name' => 'Las Minas', 'code' => 'LMI'],
                ['name' => 'Nuestra Señora del Rosario', 'code' => 'NSR'],
            ],
            'BOL' => [ // Bolívar
                ['name' => 'Mariche', 'code' => 'MAR'],
            ],
            'BRI' => [ // Brión
                ['name' => 'Higuerote', 'code' => 'HIG'],
                ['name' => 'Curiepe', 'code' => 'CUR'],
                ['name' => 'Tacarigua de Brión', 'code' => 'TAB'],
                ['name' => 'Chirimena', 'code' => 'CHI'],
                ['name' => 'Birongo', 'code' => 'BIR'],
            ],
            'BUR' => [ // Buroz
                ['name' => 'Mamporal', 'code' => 'MAM'],
            ],
            'CAR' => [ // Carrizal
                ['name' => 'Carrizal', 'code' => 'CAR'],
            ],
            'CHA' => [ // Chacao
                ['name' => 'Chacao', 'code' => 'CHA'],
            ],
            'CRO' => [ // Cristóbal Rojas
                ['name' => 'Charallave', 'code' => 'CHA'],
                ['name' => 'Las Brisas', 'code' => 'LBR'],
            ],
            'HAT' => [ // El Hatillo
                ['name' => 'Santa Rosalía de Palermo', 'code' => 'SRP'],
            ],
            'GUA' => [ // Guaicaipuro
                ['name' => 'Altagracia de la Montaña', 'code' => 'ALM'],
                ['name' => 'Cecilio Acosta', 'code' => 'CAC'],
                ['name' => 'Los Teques', 'code' => 'LTE'],
                ['name' => 'El Jarillo', 'code' => 'EJA'],
                ['name' => 'San Pedro', 'code' => 'SPE'],
                ['name' => 'Tácata', 'code' => 'TAC'],
                ['name' => 'Paracotos', 'code' => 'PAR'],
            ],
            'GUL' => [ // Gual
                ['name' => 'Cúpira', 'code' => 'CUP'],
                ['name' => 'Machurucuto', 'code' => 'MAC'],
                ['name' => 'Guarabe', 'code' => 'GUA'],
            ],
            'IND' => [ // Independencia
                ['name' => 'El Cartanal', 'code' => 'ECA'],
                ['name' => 'Santa Teresa del Tuy', 'code' => 'STT'],
            ],
            'LAN' => [ // Lander
                ['name' => 'La Democracia', 'code' => 'LDE'],
                ['name' => 'Ocumare del Tuy', 'code' => 'ODT'],
                ['name' => 'Santa Bárbara', 'code' => 'SBA'],
                ['name' => 'La Mata', 'code' => 'LMA'],
                ['name' => 'La Cabrera', 'code' => 'LCA'],
            ],
            'LSA' => [ // Los Salias
                ['name' => 'San Antonio de los Altos', 'code' => 'SAA'],
            ],
            'PAE' => [ // Páez
                ['name' => 'Río Chico', 'code' => 'RCH'],
                ['name' => 'El Guapo', 'code' => 'EGU'],
                ['name' => 'Tacarigua de la Laguna', 'code' => 'TAL'],
                ['name' => 'Paparo', 'code' => 'PAP'],
                ['name' => 'San Fernando del Guapo', 'code' => 'SFG'],
            ],
            'PCA' => [ // Paz Castillo
                ['name' => 'Santa Lucía del Tuy', 'code' => 'SLT'],
                ['name' => 'Santa Rita', 'code' => 'SRI'],
                ['name' => 'Siquire', 'code' => 'SIQ'],
                ['name' => 'Soapire', 'code' => 'SOA'],
            ],
            'PLA' => [ // Plaza
                ['name' => 'Guarenas', 'code' => 'GUA'],
            ],
            'SUC' => [ // Sucre
                ['name' => 'Leoncio Martínez', 'code' => 'LMA'],
                ['name' => 'Caucagüita', 'code' => 'CAU'],
                ['name' => 'Filas de Mariche', 'code' => 'FMA'],
                ['name' => 'La Dolorita', 'code' => 'LDO'],
                ['name' => 'Petare', 'code' => 'PET'],
            ],
            'URD' => [ // Urdaneta
                ['name' => 'Cúa', 'code' => 'CUA'],
                ['name' => 'Nueva Cúa', 'code' => 'NCU'],
            ],
            'ZAM' => [ // Zamora
                ['name' => 'Guatire', 'code' => 'GUA'],
                ['name' => 'Araira', 'code' => 'ARA'],
            ],

            // CARABOBO
            'BEJ' => [ // Bejuma
                ['name' => 'Bejuma', 'code' => 'BEJ'],
                ['name' => 'Canoabo', 'code' => 'CAN'],
                ['name' => 'Simón Bolívar', 'code' => 'SBO'],
            ],
            'CAR' => [ // Carlos Arvelo
                ['name' => 'Güigüe', 'code' => 'GUI'],
                ['name' => 'Belén', 'code' => 'BEL'],
            ],
            'DIB' => [ // Diego Ibarra
                ['name' => 'Mariara', 'code' => 'MAR'],
                ['name' => 'Aguas Calientes', 'code' => 'AGC'],
            ],
            'GUA' => [ // Guacara
                ['name' => 'Ciudad Alianza', 'code' => 'CAL'],
                ['name' => 'Guacara', 'code' => 'GUA'],
                ['name' => 'Yagua', 'code' => 'YAG'],
            ],
            'JJM' => [ // Juan José Mora
                ['name' => 'Morón', 'code' => 'MOR'],
                ['name' => 'Urama', 'code' => 'URA'],
            ],
            'LIB' => [ // Libertador
                ['name' => 'Tocuyito', 'code' => 'TOC'],
                ['name' => 'Independencia', 'code' => 'IND'],
            ],
            'LGU' => [ // Los Guayos
                ['name' => 'Los Guayos', 'code' => 'LGU'],
            ],
            'MIR' => [ // Miranda
                ['name' => 'Miranda', 'code' => 'MIR'],
            ],
            'MON' => [ // Montalbán
                ['name' => 'Montalbán', 'code' => 'MON'],
            ],
            'NAG' => [ // Naguanagua
                ['name' => 'Naguanagua', 'code' => 'NAG'],
            ],
            'PCA' => [ // Puerto Cabello
                ['name' => 'Bartolomé Salóm', 'code' => 'BSA'],
                ['name' => 'Democracia', 'code' => 'DEM'],
                ['name' => 'Fraternidad', 'code' => 'FRA'],
                ['name' => 'Goaigoaza', 'code' => 'GOA'],
                ['name' => 'Juan José Flores', 'code' => 'JJF'],
                ['name' => 'Unión', 'code' => 'UNI'],
                ['name' => 'Borburata', 'code' => 'BOR'],
                ['name' => 'Patanemo', 'code' => 'PAT'],
            ],
            'SDI' => [ // San Diego
                ['name' => 'San Diego', 'code' => 'SDI'],
            ],
            'SJO' => [ // San Joaquín
                ['name' => 'San Joaquín', 'code' => 'SJO'],
            ],
            'VAL' => [ // Valencia
                ['name' => 'Candelaria', 'code' => 'CAN'],
                ['name' => 'Catedral', 'code' => 'CAT'],
                ['name' => 'El Socorro', 'code' => 'ESO'],
                ['name' => 'Miguel Peña', 'code' => 'MPE'],
                ['name' => 'Rafael Urdaneta', 'code' => 'RUR'],
                ['name' => 'San Blas', 'code' => 'SBL'],
                ['name' => 'San José', 'code' => 'SJO'],
                ['name' => 'Santa Rosa', 'code' => 'SRO'],
                ['name' => 'Negro Primero', 'code' => 'NPR'],
            ],

            // ZULIA - Todas las parroquias de todos los municipios
            'APA' => [ // Almirante Padilla
                ['name' => 'Isla de Toas', 'code' => 'IDT'],
                ['name' => 'Monagas', 'code' => 'MON'],
            ],
            'BAR' => [ // Baralt
                ['name' => 'San Timoteo', 'code' => 'STI'],
                ['name' => 'General Urdaneta', 'code' => 'GUR'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Marcelino Briceño', 'code' => 'MBR'],
                ['name' => 'Pueblo Nuevo', 'code' => 'PNU'],
                ['name' => 'Manuel Guanipa Matos', 'code' => 'MGM'],
            ],
            'CAB' => [ // Cabimas
                ['name' => 'Ambrosio', 'code' => 'AMB'],
                ['name' => 'Arístides Calvani', 'code' => 'ACA'],
                ['name' => 'Carmen Herrera', 'code' => 'CHE'],
                ['name' => 'Germán Ríos Linares', 'code' => 'GRL'],
                ['name' => 'Jorge Hernández', 'code' => 'JHE'],
                ['name' => 'La Rosa', 'code' => 'LRO'],
                ['name' => 'Punta Gorda', 'code' => 'PGO'],
                ['name' => 'Rómulo Betancourt', 'code' => 'RBE'],
                ['name' => 'San Benito', 'code' => 'SBE'],
            ],
            'CAT' => [ // Catatumbo
                ['name' => 'Encontrados', 'code' => 'ENC'],
                ['name' => 'Udón Pérez', 'code' => 'UPE'],
            ],
            'COL' => [ // Colón
                ['name' => 'San Carlos del Zulia', 'code' => 'SCZ'],
                ['name' => 'Moralito', 'code' => 'MOR'],
                ['name' => 'Santa Bárbara', 'code' => 'SBA'],
                ['name' => 'Santa Cruz del Zulia', 'code' => 'SCR'],
                ['name' => 'Urribarrí', 'code' => 'URR'],
            ],
            'FJP' => [ // Francisco Javier Pulgar
                ['name' => 'Simón Rodríguez', 'code' => 'SRO'],
                ['name' => 'Agustín Codazzi', 'code' => 'ACO'],
                ['name' => 'Carlos Quevedo', 'code' => 'CQU'],
                ['name' => 'Francisco Javier Pulgar', 'code' => 'FJP'],
            ],
            'GUA' => [ // Guajira
                ['name' => 'Sinamaica', 'code' => 'SIN'],
                ['name' => 'Alta Guajira', 'code' => 'AGU'],
                ['name' => 'Elías Sánchez Rubio', 'code' => 'ESR'],
                ['name' => 'Guajira', 'code' => 'GUA'],
            ],
            'JEL' => [ // Jesús Enrique Lossada
                ['name' => 'La Concepción', 'code' => 'LCO'],
                ['name' => 'San José', 'code' => 'SJO'],
                ['name' => 'Mariano Parra León', 'code' => 'MPL'],
                ['name' => 'José Ramón Yépez', 'code' => 'JRY'],
            ],
            'JMS' => [ // Jesús María Semprún
                ['name' => 'Jesús María Semprún', 'code' => 'JMS'],
                ['name' => 'Barí', 'code' => 'BAR'],
            ],
            'LCU' => [ // La Cañada de Urdaneta
                ['name' => 'Concepción', 'code' => 'CON'],
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
                ['name' => 'Chiquinquirá', 'code' => 'CHI'],
                ['name' => 'El Carmelo', 'code' => 'ECA'],
                ['name' => 'Potreritos', 'code' => 'POT'],
            ],
            'LAG' => [ // Lagunillas
                ['name' => 'Alonso de Ojeda', 'code' => 'ADO'],
                ['name' => 'Libertad', 'code' => 'LIB'],
                ['name' => 'Eleazar López Contreras', 'code' => 'ELC'],
                ['name' => 'Campo Lara', 'code' => 'CLA'],
                ['name' => 'Venezuela', 'code' => 'VEN'],
                ['name' => 'El Danto', 'code' => 'EDA'],
            ],
            'MDP' => [ // Machiques de Perijá
                ['name' => 'Libertad', 'code' => 'LIB2'],
                ['name' => 'Bartolomé de las Casas', 'code' => 'BCA'],
                ['name' => 'Río Negro', 'code' => 'RNE'],
                ['name' => 'San José de Perijá', 'code' => 'SJP'],
            ],
            'MAR' => [ // Mara
                ['name' => 'San Rafael', 'code' => 'SRA'],
                ['name' => 'La Sierrita', 'code' => 'LSI'],
                ['name' => 'Las Parcelas', 'code' => 'LPA'],
                ['name' => 'Luis De Vicente', 'code' => 'LDV'],
                ['name' => 'Monseñor Marcos Sergio Godoy', 'code' => 'MSG'],
                ['name' => 'Ricaurte', 'code' => 'RIC'],
                ['name' => 'Tamare', 'code' => 'TAM'],
            ],
            'MAC' => [ // Maracaibo
                ['name' => 'Antonio Borjas Romero', 'code' => 'ABR'],
                ['name' => 'Bolívar', 'code' => 'BOL'],
                ['name' => 'Cacique Mara', 'code' => 'CMA'],
                ['name' => 'Carracciolo Parra Pérez', 'code' => 'CPP'],
                ['name' => 'Cecilio Acosta', 'code' => 'CAC'],
                ['name' => 'Chiquinquirá', 'code' => 'CHI2'],
                ['name' => 'Coquivacoa', 'code' => 'COQ'],
                ['name' => 'Cristo de Aranza', 'code' => 'CDA'],
                ['name' => 'Francisco Eugenio Bustamante', 'code' => 'FEB'],
                ['name' => 'Idelfonzo Vásquez', 'code' => 'IVA'],
                ['name' => 'Juana de Ávila', 'code' => 'JDA'],
                ['name' => 'Luis Hurtado Higuera', 'code' => 'LHH'],
                ['name' => 'Manuel Dagnino', 'code' => 'MDA'],
                ['name' => 'Olegario Villalobos', 'code' => 'OVI'],
                ['name' => 'Raúl Leoni', 'code' => 'RLE'],
                ['name' => 'San Isidro', 'code' => 'SIS'],
                ['name' => 'Santa Lucía', 'code' => 'SLU'],
                ['name' => 'Venancio Pulgar', 'code' => 'VPU'],
            ],
            'MIR' => [ // Miranda
                ['name' => 'Altagracia', 'code' => 'ALT'],
                ['name' => 'Ana María Campos', 'code' => 'AMC'],
                ['name' => 'Faría', 'code' => 'FAR'],
                ['name' => 'San Antonio', 'code' => 'SAN'],
                ['name' => 'San José', 'code' => 'SJO2'],
            ],
            'RDP' => [ // Rosario de Perijá
                ['name' => 'El Rosario', 'code' => 'ERO'],
                ['name' => 'Donaldo García', 'code' => 'DGA'],
                ['name' => 'Sixto Zambrano', 'code' => 'SZA'],
            ],
            'SFR' => [ // San Francisco
                ['name' => 'San Francisco', 'code' => 'SFR'],
                ['name' => 'El Bajo', 'code' => 'EBA'],
                ['name' => 'Domitila Flores', 'code' => 'DFL'],
                ['name' => 'Francisco Ochoa', 'code' => 'FOC'],
                ['name' => 'Los Cortijos', 'code' => 'LCO'],
                ['name' => 'Marcial Hernández', 'code' => 'MHE'],
                ['name' => 'José Domingo Rus', 'code' => 'JDR'],
            ],
            'SRI' => [ // Santa Rita
                ['name' => 'Santa Rita', 'code' => 'SRI'],
                ['name' => 'El Mene', 'code' => 'EME'],
                ['name' => 'José Cenobio Urribarrí', 'code' => 'JCU'],
                ['name' => 'Pedro Lucas Urribarrí', 'code' => 'PLU'],
            ],
            'SBO' => [ // Simón Bolívar
                ['name' => 'Manuel Manrique', 'code' => 'MMA'],
                ['name' => 'Rafael Maria Baralt', 'code' => 'RMB'],
                ['name' => 'Rafael Urdaneta', 'code' => 'RUR'],
            ],
            'SUC' => [ // Sucre
                ['name' => 'Bobures', 'code' => 'BOB'],
                ['name' => 'El Batey', 'code' => 'EBA2'],
                ['name' => 'Gibraltar', 'code' => 'GIB'],
                ['name' => 'Heras', 'code' => 'HER'],
                ['name' => 'Monseñor Arturo Álvarez', 'code' => 'MAA'],
                ['name' => 'Rómulo Gallegos', 'code' => 'RGA'],
            ],
            'VRO' => [ // Valmore Rodríguez
                ['name' => 'Rafael Urdaneta', 'code' => 'RUR2'],
                ['name' => 'La Victoria', 'code' => 'LVI'],
                ['name' => 'Raúl Cuenca', 'code' => 'RCU'],
            ],

            // ARAGUA - Todas las parroquias de todos los municipios
            'ALC' => [ // Alcántara
                ['name' => 'Alcántara', 'code' => 'ALC'],
            ],
            'BOL' => [ // Bolívar
                ['name' => 'San Mateo', 'code' => 'SMA'],
            ],
            'CAM' => [ // Camatagua
                ['name' => 'Camatagua', 'code' => 'CAM'],
                ['name' => 'Carmen de Cura', 'code' => 'CDC'],
            ],
            'GIR' => [ // Girardot
                ['name' => 'Maracay', 'code' => 'MAR'],
                ['name' => 'San Jacinto', 'code' => 'SJA'],
                ['name' => 'Madre María de San José', 'code' => 'MMS'],
                ['name' => 'Andrés Eloy Blanco', 'code' => 'AEB'],
                ['name' => 'Los Tacarigua', 'code' => 'LTA'],
                ['name' => 'Las Delicias', 'code' => 'LDE'],
                ['name' => 'Choroní', 'code' => 'CHO'],
            ],
            'IRA' => [ // Iragorry
                ['name' => 'El Limón', 'code' => 'ELI'],
                ['name' => 'Caña de Azúcar', 'code' => 'CDA'],
            ],
            'LAM' => [ // Lamas
                ['name' => 'Santa Cruz de Aragua', 'code' => 'SCA'],
                ['name' => 'San Lúcas', 'code' => 'SLU'],
            ],
            'LIB' => [ // Libertador
                ['name' => 'Palo Negro', 'code' => 'PNE'],
                ['name' => 'San Martín de Porres', 'code' => 'SMP'],
            ],
            'MAR' => [ // Mariño
                ['name' => 'Turmero', 'code' => 'TUR'],
                ['name' => 'Arevalo Aponte', 'code' => 'AAP'],
                ['name' => 'Chuao', 'code' => 'CHU'],
                ['name' => 'Samán de Güere', 'code' => 'SGU'],
                ['name' => 'Alfredo Pacheco Miranda', 'code' => 'APM'],
            ],
            'OCU' => [ // Ocumare de la Costa de Oro
                ['name' => 'Ocumare de la Costa', 'code' => 'ODC'],
            ],
            'REV' => [ // Revenga
                ['name' => 'El Consejo', 'code' => 'ECO'],
            ],
            'RIB' => [ // Ribas
                ['name' => 'La Victoria', 'code' => 'LVI'],
                ['name' => 'Zuata', 'code' => 'ZUA'],
                ['name' => 'Pedro José Ovalles', 'code' => 'PJO'],
                ['name' => 'José Casanova Godoy', 'code' => 'JCG'],
                ['name' => 'Las Mercedes', 'code' => 'LME'],
                ['name' => 'José Félix Ribas', 'code' => 'JFR'],
                ['name' => 'Castor Nieves Ríos', 'code' => 'CNR'],
            ],
            'SCA' => [ // San Casimiro
                ['name' => 'San Casimiro', 'code' => 'SCA'],
                ['name' => 'Güiripa', 'code' => 'GUI'],
                ['name' => 'Ollas de Caramacate', 'code' => 'ODC'],
            ],
            'SSE' => [ // San Sebastián
                ['name' => 'San Sebastián', 'code' => 'SSE'],
            ],
            'SMA' => [ // Santiago Mariño
                ['name' => 'Turmero', 'code' => 'TUR2'],
                ['name' => 'Arevalo Aponte', 'code' => 'AAP2'],
                ['name' => 'Chuao', 'code' => 'CHU2'],
                ['name' => 'Samán de Güere', 'code' => 'SGU2'],
                ['name' => 'Alfredo Pacheco Miranda', 'code' => 'APM2'],
            ],
            'SMI' => [ // Santos Michelena
                ['name' => 'Las Tejerías', 'code' => 'LTE'],
                ['name' => 'Tiara', 'code' => 'TIA'],
            ],
            'SUC' => [ // Sucre
                ['name' => 'Cagua', 'code' => 'CAG'],
                ['name' => 'Bella Vista', 'code' => 'BVI'],
            ],
            'TOV' => [ // Tovar
                ['name' => 'Colonia Tovar', 'code' => 'CTO'],
            ],
            'URD' => [ // Urdaneta
                ['name' => 'Barbacoas', 'code' => 'BAR'],
                ['name' => 'San Francisco de Cara', 'code' => 'SFC'],
                ['name' => 'Taguay', 'code' => 'TAG'],
                ['name' => 'Las Peñitas', 'code' => 'LPE'],
            ],

            // ANZOÁTEGUI - Todas las parroquias de todos los municipios
            'ANA' => [ // Anaco
                ['name' => 'Anaco', 'code' => 'ANA'],
                ['name' => 'San Joaquín', 'code' => 'SJO'],
                ['name' => 'Buena Vista', 'code' => 'BVI'],
            ],
            'ARA' => [ // Aragua
                ['name' => 'Aragua de Barcelona', 'code' => 'ADB'],
                ['name' => 'Cachipo', 'code' => 'CAC'],
            ],
            'BOL' => [ // Bolívar (Barcelona)
                ['name' => 'Barcelona', 'code' => 'BAR'],
                ['name' => 'El Carmen', 'code' => 'ECA'],
                ['name' => 'San Cristóbal', 'code' => 'SCR'],
                ['name' => 'Bergantín', 'code' => 'BER'],
                ['name' => 'Caigua', 'code' => 'CAI'],
                ['name' => 'El Pilar', 'code' => 'EPI'],
                ['name' => 'Naricual', 'code' => 'NAR'],
            ],
            'BRU' => [ // Bruzual
                ['name' => 'Clarines', 'code' => 'CLA'],
                ['name' => 'Guanape', 'code' => 'GUA'],
                ['name' => 'Sabana de Uchire', 'code' => 'SDU'],
            ],
            'CAJ' => [ // Cajigal
                ['name' => 'Onoto', 'code' => 'ONO'],
                ['name' => 'San Pablo', 'code' => 'SPA'],
            ],
            'CAR' => [ // Carvajal
                ['name' => 'Valle de Guanape', 'code' => 'VDG'],
                ['name' => 'Santa Bárbara', 'code' => 'SBA'],
            ],
            'FRE' => [ // Freites
                ['name' => 'Cantaura', 'code' => 'CAN'],
                ['name' => 'Libertador', 'code' => 'LIB'],
                ['name' => 'Santa Rosa', 'code' => 'SRO'],
                ['name' => 'Urica', 'code' => 'URI'],
            ],
            'GUA' => [ // Guanipa
                ['name' => 'San José de Guanipa', 'code' => 'SJG'],
            ],
            'GAN' => [ // Guanta
                ['name' => 'Guanta', 'code' => 'GAN'],
                ['name' => 'Chorrerón', 'code' => 'CHO'],
            ],
            'IND' => [ // Independencia
                ['name' => 'Soledad', 'code' => 'SOL'],
                ['name' => 'Mamo', 'code' => 'MAM'],
            ],
            'LIB' => [ // Libertad
                ['name' => 'San Mateo', 'code' => 'SMA2'],
                ['name' => 'El Carito', 'code' => 'ECA2'],
                ['name' => 'Santa Inés', 'code' => 'SIN2'],
            ],
            'SAM' => [ // Sir Arthur McGregor
                ['name' => 'El Chaparro', 'code' => 'ECH'],
                ['name' => 'Tomás Alfaro Calatrava', 'code' => 'TAC'],
            ],
            'MIR' => [ // Miranda
                ['name' => 'Pariaguán', 'code' => 'PAR'],
                ['name' => 'Atapirire', 'code' => 'ATA'],
                ['name' => 'Boca del Pao', 'code' => 'BDP'],
                ['name' => 'El Pao', 'code' => 'EPA'],
            ],
            'MON' => [ // Monagas
                ['name' => 'Mapire', 'code' => 'MAP'],
                ['name' => 'Piar', 'code' => 'PIA'],
                ['name' => 'Santa Clara', 'code' => 'SCL'],
                ['name' => 'San Diego de Cabrutica', 'code' => 'SDC'],
                ['name' => 'Uverito', 'code' => 'UVE'],
                ['name' => 'Zuata', 'code' => 'ZUA2'],
            ],
            'PEN' => [ // Peñalver
                ['name' => 'Puerto Píritu', 'code' => 'PPR'],
                ['name' => 'San Miguel', 'code' => 'SMI'],
                ['name' => 'Sucre', 'code' => 'SUC2'],
            ],
            'PIR' => [ // Píritu
                ['name' => 'Píritu', 'code' => 'PIR'],
                ['name' => 'San Francisco', 'code' => 'SFR2'],
            ],
            'SJC' => [ // San Juan de Capistrano
                ['name' => 'Boca de Uchire', 'code' => 'BDU'],
                ['name' => 'Boca de Chávez', 'code' => 'BDC'],
            ],
            'SAN' => [ // Santa Ana
                ['name' => 'Santa Ana', 'code' => 'SAN'],
                ['name' => 'Pueblo Nuevo', 'code' => 'PNU'],
            ],
            'SRO' => [ // Simón Rodríguez
                ['name' => 'El Tigre', 'code' => 'ETI'],
                ['name' => 'Edmundo Barrios', 'code' => 'EBA'],
                ['name' => 'Miguel Otero Silva', 'code' => 'MOS'],
            ],
            'SOT' => [ // Sotillo
                ['name' => 'Puerto La Cruz', 'code' => 'PLC'],
                ['name' => 'Pozuelos', 'code' => 'POZ'],
            ],
            'TDB' => [ // Turístico Diego Bautista Urbaneja
                ['name' => 'Lechería', 'code' => 'LEC'],
                ['name' => 'El Morro', 'code' => 'EMO'],
            ],

            // APURE - Todas las parroquias de todos los municipios
            'ACH' => [ // Achaguas
                ['name' => 'Achaguas', 'code' => 'ACH'],
                ['name' => 'Apurito', 'code' => 'APU'],
                ['name' => 'El Yagual', 'code' => 'EYA'],
                ['name' => 'Guachara', 'code' => 'GUA'],
                ['name' => 'Mucuritas', 'code' => 'MUC'],
                ['name' => 'Queseras del Medio', 'code' => 'QDM'],
            ],
            'BIR' => [ // Biruaca
                ['name' => 'Biruaca', 'code' => 'BIR'],
            ],
            'PCM' => [ // Pedro Camejo
                ['name' => 'San Juan de Payara', 'code' => 'SJP'],
                ['name' => 'Codazzi', 'code' => 'COD'],
                ['name' => 'Cunaviche', 'code' => 'CUN'],
            ],
            'MUN' => [ // Muñoz
                ['name' => 'Bruzual', 'code' => 'BRU'],
                ['name' => 'Mantecal', 'code' => 'MAN'],
                ['name' => 'Quintero', 'code' => 'QUI'],
                ['name' => 'Rincón Hondo', 'code' => 'RHO'],
                ['name' => 'San Vicente', 'code' => 'SVI'],
            ],
            'PAE' => [ // Páez
                ['name' => 'Guasdualito', 'code' => 'GUA'],
                ['name' => 'Aramendi', 'code' => 'ARA'],
                ['name' => 'El Amparo', 'code' => 'EAM'],
                ['name' => 'San Camilo', 'code' => 'SCA'],
                ['name' => 'Urdaneta', 'code' => 'URD'],
            ],
            'RGA' => [ // Rómulo Gallegos
                ['name' => 'Elorza', 'code' => 'ELO'],
                ['name' => 'La Trinidad', 'code' => 'LTR'],
            ],
            'SFE' => [ // San Fernando
                ['name' => 'San Fernando', 'code' => 'SFE'],
                ['name' => 'El Recreo', 'code' => 'ERE'],
                ['name' => 'Peñalver', 'code' => 'PEN'],
                ['name' => 'San Rafael de Atamaica', 'code' => 'SRA'],
            ],

            // BARINAS - Todas las parroquias de todos los municipios
            'ATO' => [ // Alberto Torrealba
                ['name' => 'Sosa', 'code' => 'SOS'],
            ],
            'AEB' => [ // Andrés Eloy Blanco
                ['name' => 'El Cantón', 'code' => 'ECA'],
                ['name' => 'Santa Cruz de Guacas', 'code' => 'SCG'],
                ['name' => 'Puerto Vivas', 'code' => 'PVI'],
            ],
            'AJS' => [ // Antonio José de Sucre
                ['name' => 'Ticoporo', 'code' => 'TIC'],
                ['name' => 'Nicolás Pulido', 'code' => 'NPU'],
                ['name' => 'Andrés Bello', 'code' => 'ABE'],
            ],
            'ARI' => [ // Arismendi
                ['name' => 'Arismendi', 'code' => 'ARI'],
                ['name' => 'Guadarrama', 'code' => 'GUA'],
                ['name' => 'La Unión', 'code' => 'LUN'],
                ['name' => 'San Antonio', 'code' => 'SAN'],
            ],
            'BAR' => [ // Barinas
                ['name' => 'Barinas', 'code' => 'BAR'],
                ['name' => 'Alberto Torrealba', 'code' => 'ATO'],
                ['name' => 'San Silvestre', 'code' => 'SSI'],
                ['name' => 'Santa Inés', 'code' => 'SIN'],
                ['name' => 'Santa Lucía', 'code' => 'SLU'],
                ['name' => 'Torunos', 'code' => 'TOR'],
                ['name' => 'El Carmen', 'code' => 'ECA2'],
                ['name' => 'Rómulo Betancourt', 'code' => 'RBE'],
                ['name' => 'Corazón de Jesús', 'code' => 'CDJ'],
                ['name' => 'Ramón Ignacio Méndez', 'code' => 'RIM'],
                ['name' => 'Alto Barinas', 'code' => 'ABA'],
                ['name' => 'Manuel Palacio Fajardo', 'code' => 'MPF'],
                ['name' => 'Juan Antonio Rodríguez Domínguez', 'code' => 'JAR'],
                ['name' => 'Dominga Ortiz de Páez', 'code' => 'DOP'],
            ],
            'BOL' => [ // Bolívar
                ['name' => 'Barinitas', 'code' => 'BAR2'],
                ['name' => 'Altamira de Cáceres', 'code' => 'ADC'],
                ['name' => 'Calderas', 'code' => 'CAL'],
            ],
            'CPA' => [ // Cruz Paredes
                ['name' => 'Barrancas', 'code' => 'BAR3'],
                ['name' => 'El Socorro', 'code' => 'ESO'],
                ['name' => 'Masparrito', 'code' => 'MAS'],
            ],
            'EZA' => [ // Ezequiel Zamora
                ['name' => 'Santa Bárbara', 'code' => 'SBA'],
                ['name' => 'Pedro Briceño Méndez', 'code' => 'PBM'],
                ['name' => 'Ramón Ignacio Méndez', 'code' => 'RIM2'],
                ['name' => 'José Ignacio del Pumar', 'code' => 'JIP'],
            ],
            'OBI' => [ // Obispos
                ['name' => 'Obispos', 'code' => 'OBI'],
                ['name' => 'Guasimitos', 'code' => 'GUA2'],
                ['name' => 'El Real', 'code' => 'ERE2'],
                ['name' => 'La Luz', 'code' => 'LLU'],
            ],
            'PED' => [ // Pedraza
                ['name' => 'Ciudad Bolivia', 'code' => 'CBO'],
                ['name' => 'José Ignacio Pumar', 'code' => 'JIP2'],
                ['name' => 'Pedro Briceño Méndez', 'code' => 'PBM2'],
            ],
            'ROJ' => [ // Rojas
                ['name' => 'Libertad', 'code' => 'LIB2'],
                ['name' => 'Dolores', 'code' => 'DOL'],
                ['name' => 'Santa Rosa', 'code' => 'SRO2'],
                ['name' => 'Palacio Fajardo', 'code' => 'PFA'],
            ],
            'SOS' => [ // Sosa
                ['name' => 'Ciudad de Nutrias', 'code' => 'CDN'],
                ['name' => 'El Regalo', 'code' => 'ERE3'],
                ['name' => 'Puerto Nutrias', 'code' => 'PNU2'],
                ['name' => 'Santa Catalina', 'code' => 'SCA2'],
            ],

            // BOLÍVAR - Todas las parroquias de todos los municipios
            'ANG' => [ // Angostura
                ['name' => 'Dalla Costa', 'code' => 'DCO'],
                ['name' => 'San Isidro', 'code' => 'SIS2'],
            ],
            'CAR' => [ // Caroní
                ['name' => 'Cachamay', 'code' => 'CAC'],
                ['name' => 'Chirica', 'code' => 'CHI'],
                ['name' => 'Dalla Costa', 'code' => 'DCO2'],
                ['name' => 'Once de Abril', 'code' => 'ODA'],
                ['name' => 'Simón Bolívar', 'code' => 'SBO2'],
                ['name' => 'Unare', 'code' => 'UNA'],
                ['name' => 'Universidad', 'code' => 'UNI'],
                ['name' => 'Vista al Sol', 'code' => 'VAS'],
                ['name' => 'Pozo Verde', 'code' => 'PVE'],
                ['name' => 'Yocoima', 'code' => 'YOC'],
                ['name' => '5 de Julio', 'code' => '5DJ'],
            ],
            'CED' => [ // Cedeño
                ['name' => 'Cedeño', 'code' => 'CED'],
                ['name' => 'Altagracia', 'code' => 'ALT2'],
                ['name' => 'La Urbana', 'code' => 'LUR'],
                ['name' => 'Guaniamo', 'code' => 'GUA3'],
                ['name' => 'Pijiguaos', 'code' => 'PIJ'],
            ],
            'ECA' => [ // El Callao
                ['name' => 'El Callao', 'code' => 'ECA3'],
            ],
            'GSA' => [ // Gran Sabana
                ['name' => 'Santa Elena de Uairén', 'code' => 'SEU'],
                ['name' => 'Ikabaru', 'code' => 'IKA'],
            ],
            'HER' => [ // Heres
                ['name' => 'Catedral', 'code' => 'CAT2'],
                ['name' => 'Zea', 'code' => 'ZEA'],
                ['name' => 'Orinoco', 'code' => 'ORI'],
                ['name' => 'José Antonio Páez', 'code' => 'JAP'],
                ['name' => 'Marhuanta', 'code' => 'MAR2'],
                ['name' => 'Agua Salada', 'code' => 'ASA'],
                ['name' => 'Vista Hermosa', 'code' => 'VHE'],
                ['name' => 'La Sabanita', 'code' => 'LSA'],
                ['name' => 'Panapana', 'code' => 'PAN2'],
            ],
            'PIA' => [ // Piar
                ['name' => 'Pedro Cova', 'code' => 'PCO'],
                ['name' => 'Andrés Eloy Blanco', 'code' => 'AEB2'],
            ],
            'ROS' => [ // Roscio
                ['name' => 'Guasipati', 'code' => 'GUA4'],
                ['name' => 'Salom', 'code' => 'SAL'],
            ],
            'SIF' => [ // Sifontes
                ['name' => 'Tumeremo', 'code' => 'TUM'],
                ['name' => 'Dalla Costa', 'code' => 'DCO3'],
                ['name' => 'San Isidro', 'code' => 'SIS3'],
            ],
            'SUC' => [ // Sucre
                ['name' => 'Maripa', 'code' => 'MAR3'],
                ['name' => 'Aripao', 'code' => 'ARI2'],
                ['name' => 'Las Majadas', 'code' => 'LMA2'],
                ['name' => 'Moitaco', 'code' => 'MOI'],
            ],
            'PPC' => [ // Padre Pedro Chien
                ['name' => 'Padre Pedro Chien', 'code' => 'PPC'],
                ['name' => 'Río Grande', 'code' => 'RGR'],
            ],

            // LA GUAIRA - Todas las parroquias
            'VAR' => [ // Vargas
                ['name' => 'Caraballeda', 'code' => 'CAR2'],
                ['name' => 'Carayaca', 'code' => 'CAR3'],
                ['name' => 'Carlos Soublette', 'code' => 'CSO'],
                ['name' => 'Caruao', 'code' => 'CAR4'],
                ['name' => 'Catia La Mar', 'code' => 'CLM'],
                ['name' => 'El Junko', 'code' => 'EJU2'],
                ['name' => 'La Guaira', 'code' => 'LGU2'],
                ['name' => 'Macuto', 'code' => 'MAC2'],
                ['name' => 'Maiquetía', 'code' => 'MAI'],
                ['name' => 'Naiguatá', 'code' => 'NAI'],
                ['name' => 'Urimare', 'code' => 'URI2'],
            ],

            // LARA - Todas las parroquias de todos los municipios
            'AEB' => [ // Andrés Eloy Blanco
                ['name' => 'Quebrada Honda de Guache', 'code' => 'QHG'],
                ['name' => 'Pío Tamayo', 'code' => 'PTA'],
                ['name' => 'Yacambú', 'code' => 'YAC'],
            ],
            'CRE' => [ // Crespo
                ['name' => 'Freitez', 'code' => 'FRE'],
                ['name' => 'José María Blanco', 'code' => 'JMB'],
            ],
            'IRI' => [ // Iribarren
                ['name' => 'Catedral', 'code' => 'CAT3'],
                ['name' => 'Concepción', 'code' => 'CON2'],
                ['name' => 'El Cují', 'code' => 'ECU'],
                ['name' => 'Juan de Villegas', 'code' => 'JVL'],
                ['name' => 'Santa Rosa', 'code' => 'SRO3'],
                ['name' => 'Tamaca', 'code' => 'TAM2'],
                ['name' => 'Unión', 'code' => 'UNI2'],
                ['name' => 'Aguedo Felipe Alvarado', 'code' => 'AFA'],
                ['name' => 'Buena Vista', 'code' => 'BVI2'],
                ['name' => 'Juárez', 'code' => 'JUA'],
            ],
            'JIM' => [ // Jiménez
                ['name' => 'Quíbor', 'code' => 'QUI2'],
                ['name' => 'Tintorero', 'code' => 'TIN'],
                ['name' => 'Vargas', 'code' => 'VAR2'],
            ],
            'MOR' => [ // Morán
                ['name' => 'Bolívar', 'code' => 'BOL2'],
                ['name' => 'Anzoátegui', 'code' => 'ANZ'],
                ['name' => 'Guarico', 'code' => 'GUA5'],
                ['name' => 'Hilario Luna y Luna', 'code' => 'HLL'],
                ['name' => 'Humocaro Alto', 'code' => 'HAL'],
                ['name' => 'Humocaro Bajo', 'code' => 'HBA'],
                ['name' => 'La Candelaria', 'code' => 'LCA2'],
                ['name' => 'Morán', 'code' => 'MOR2'],
            ],
            'PAL' => [ // Palavecino
                ['name' => 'Cabudare', 'code' => 'CAB2'],
                ['name' => 'José Gregorio Bastidas', 'code' => 'JGB'],
                ['name' => 'Agua Viva', 'code' => 'AVI'],
            ],
            'SPL' => [ // Simón Planas
                ['name' => 'Sarare', 'code' => 'SAR'],
                ['name' => 'Buría', 'code' => 'BUR2'],
                ['name' => 'Gustavo Vegas León', 'code' => 'GVL'],
            ],
            'TOR' => [ // Torres
                ['name' => 'Carora', 'code' => 'CAR5'],
                ['name' => 'Santa Rosa', 'code' => 'SRO4'],
                ['name' => 'Tamaca', 'code' => 'TAM3'],
                ['name' => 'Antonio Díaz', 'code' => 'ADI2'],
                ['name' => 'Camacaro', 'code' => 'CAM2'],
                ['name' => 'Castañeda', 'code' => 'CAS2'],
                ['name' => 'Cecilio Zubillaga', 'code' => 'CZU'],
                ['name' => 'Chiquinquirá', 'code' => 'CHI3'],
                ['name' => 'El Empedrado', 'code' => 'EEM'],
                ['name' => 'Milagro de San José', 'code' => 'MSJ'],
                ['name' => 'Río Tocuyo', 'code' => 'RTO'],
                ['name' => 'San Miguel', 'code' => 'SMI2'],
                ['name' => 'Tinaco', 'code' => 'TIN2'],
                ['name' => 'Trinidad Samuel', 'code' => 'TSA'],
            ],
            'URD' => [ // Urdaneta
                ['name' => 'Siquisique', 'code' => 'SIQ2'],
                ['name' => 'Moroturo', 'code' => 'MOR3'],
                ['name' => 'San Miguel', 'code' => 'SMI3'],
                ['name' => 'Xaguas', 'code' => 'XAG'],
            ],

            // MÉRIDA - Todas las parroquias de todos los municipios
            'AEB' => [ // Andrés Bello
                ['name' => 'La Azulita', 'code' => 'LAZ'],
            ],
            'ARI' => [ // Arzobispo Chacón
                ['name' => 'Canaguá', 'code' => 'CAN3'],
                ['name' => 'Capurí', 'code' => 'CAP2'],
                ['name' => 'Chacantá', 'code' => 'CHA2'],
                ['name' => 'El Molino', 'code' => 'EMO2'],
                ['name' => 'Guaimaral', 'code' => 'GUA6'],
                ['name' => 'Mucutuy', 'code' => 'MUC2'],
                ['name' => 'Mucuchachí', 'code' => 'MUC3'],
            ],
            'CAM' => [ // Campo Elías
                ['name' => 'Ejido', 'code' => 'EJI'],
                ['name' => 'La Mesa', 'code' => 'LME3'],
                ['name' => 'San Juan', 'code' => 'SJU2'],
                ['name' => 'Montalbán', 'code' => 'MON2'],
                ['name' => 'Acequias', 'code' => 'ACE'],
                ['name' => 'Jají', 'code' => 'JAJ'],
            ],
            'CAR' => [ // Caracciolo Parra Olmedo
                ['name' => 'Tucaní', 'code' => 'TUC'],
                ['name' => 'Florencio Ramírez', 'code' => 'FRA2'],
            ],
            'CAR' => [ // Cardenal Quintero
                ['name' => 'Santo Domingo', 'code' => 'SDO'],
                ['name' => 'Las Piedras', 'code' => 'LPI'],
            ],
            'GUA' => [ // Guaraque
                ['name' => 'Guaraque', 'code' => 'GUA7'],
                ['name' => 'Mesa de Quintero', 'code' => 'MDQ'],
                ['name' => 'Río Negro', 'code' => 'RNE2'],
            ],
            'JUL' => [ // Julio César Salas
                ['name' => 'Aricagua', 'code' => 'ARI3'],
                ['name' => 'San Antonio', 'code' => 'SAN2'],
            ],
            'JUA' => [ // Justo Briceño
                ['name' => 'San Cristóbal de Torondoy', 'code' => 'SCT'],
                ['name' => 'Torondoy', 'code' => 'TOR2'],
            ],
            'LIB' => [ // Libertador
                ['name' => 'Arias', 'code' => 'ARI4'],
                ['name' => 'Sagrario', 'code' => 'SAG2'],
                ['name' => 'Milla', 'code' => 'MIL'],
                ['name' => 'El Llano', 'code' => 'ELL'],
                ['name' => 'Juan Rodríguez Suárez', 'code' => 'JRS'],
                ['name' => 'Jacinto Plaza', 'code' => 'JPL'],
                ['name' => 'Domingo Peña', 'code' => 'DPE'],
                ['name' => 'Gonzalo Picón Febres', 'code' => 'GPF'],
                ['name' => 'Osuna Rodríguez', 'code' => 'ORO'],
                ['name' => 'Lasso de la Vega', 'code' => 'LLV'],
                ['name' => 'Caracciolo Parra Pérez', 'code' => 'CPP2'],
                ['name' => 'Mariano Picón Salas', 'code' => 'MPS'],
                ['name' => 'Antonio Spinetti Dini', 'code' => 'ASD'],
                ['name' => 'El Morro', 'code' => 'EMO3'],
            ],
            'MIR' => [ // Miranda
                ['name' => 'Timotes', 'code' => 'TIM'],
                ['name' => 'Andrés Eloy Blanco', 'code' => 'AEB3'],
                ['name' => 'La Venta', 'code' => 'LVE2'],
                ['name' => 'Piñango', 'code' => 'PIN'],
            ],
            'OBI' => [ // Obispo Ramos de Lora
                ['name' => 'Santa Elena de Arenales', 'code' => 'SEA'],
                ['name' => 'Eloy Paredes', 'code' => 'EPA2'],
                ['name' => 'PH-8', 'code' => 'PH8'],
            ],
            'PAN' => [ // Padre Noguera
                ['name' => 'Santa María de Caparo', 'code' => 'SMC'],
            ],
            'PUE' => [ // Pueblo Llano
                ['name' => 'Pueblo Llano', 'code' => 'PLL'],
                ['name' => 'San Rafael de Alcázar', 'code' => 'SRA2'],
                ['name' => 'Tábay', 'code' => 'TAB2'],
                ['name' => 'Mucuchíes', 'code' => 'MUC4'],
                ['name' => 'Mucurubá', 'code' => 'MUC5'],
                ['name' => 'San Rafael', 'code' => 'SRA3'],
            ],
            'RAN' => [ // Rangel
                ['name' => 'Mucuchíes', 'code' => 'MUC6'],
                ['name' => 'Mucurubá', 'code' => 'MUC7'],
                ['name' => 'San Rafael', 'code' => 'SRA4'],
                ['name' => 'Cacute', 'code' => 'CAC2'],
                ['name' => 'La Toma', 'code' => 'LTO'],
            ],
            'RIV' => [ // Rivas Dávila
                ['name' => 'Bailadores', 'code' => 'BAI'],
                ['name' => 'Gerónimo Maldonado', 'code' => 'GMA'],
            ],
            'SAN' => [ // Santos Marquina
                ['name' => 'Tabay', 'code' => 'TAB3'],
            ],
            'SUC' => [ // Sucre
                ['name' => 'Lagunillas', 'code' => 'LAG2'],
                ['name' => 'Chiguará', 'code' => 'CHI4'],
                ['name' => 'Estánquez', 'code' => 'EST'],
                ['name' => 'San Juan de Lagunillas', 'code' => 'SJL'],
                ['name' => 'Pueblo Nuevo del Sur', 'code' => 'PNS'],
            ],
            'TOV' => [ // Tovar
                ['name' => 'Tovar', 'code' => 'TOV2'],
                ['name' => 'El Amparo', 'code' => 'EAM2'],
                ['name' => 'San Francisco', 'code' => 'SFR3'],
            ],
            'TUL' => [ // Tulio Febres Cordero
                ['name' => 'Nueva Bolivia', 'code' => 'NBO'],
                ['name' => 'Independencia', 'code' => 'IND2'],
                ['name' => 'María de la Concepción Palacios Blanco', 'code' => 'MCP'],
                ['name' => 'Santa Apolonia', 'code' => 'SAP'],
            ],
            'ZEA' => [ // Zea
                ['name' => 'Zea', 'code' => 'ZEA2'],
                ['name' => 'Caño El Tigre', 'code' => 'CET'],
            ],

            // MONAGAS - Todas las parroquias de todos los municipios
            'ACO' => [ // Acosta
                ['name' => 'San Lorenzo', 'code' => 'SLO'],
                ['name' => 'Yaguaraparo', 'code' => 'YAG2'],
                ['name' => 'Espíritu Santo', 'code' => 'ESP'],
            ],
            'AGU' => [ // Aguasay
                ['name' => 'Aguasay', 'code' => 'AGU2'],
            ],
            'BOL' => [ // Bolívar
                ['name' => 'Caripito', 'code' => 'CAR6'],
            ],
            'CAR' => [ // Caripe
                ['name' => 'Caripe', 'code' => 'CAR7'],
                ['name' => 'Teresen', 'code' => 'TER'],
                ['name' => 'El Guácharo', 'code' => 'EGU2'],
                ['name' => 'La Guanota', 'code' => 'LGU3'],
                ['name' => 'Sabana de Piedra', 'code' => 'SDP'],
            ],
            'CED' => [ // Cedeño
                ['name' => 'Caicara de Maturín', 'code' => 'CDM'],
                ['name' => 'Areo', 'code' => 'ARE'],
                ['name' => 'San Félix de Cantalicio', 'code' => 'SFC2'],
                ['name' => 'Viento Fresco', 'code' => 'VFR'],
            ],
            'EZA' => [ // Ezequiel Zamora
                ['name' => 'Punta de Mata', 'code' => 'PDM'],
                ['name' => 'El Tejero', 'code' => 'ETE'],
            ],
            'LIB' => [ // Libertador
                ['name' => 'Temblador', 'code' => 'TEM'],
                ['name' => 'Tabasca', 'code' => 'TAB4'],
                ['name' => 'Las Alhuacas', 'code' => 'LAL'],
                ['name' => 'Chaguaramas', 'code' => 'CHA3'],
            ],
            'MAT' => [ // Maturín
                ['name' => 'Alto de los Godos', 'code' => 'ADG'],
                ['name' => 'Boquerón', 'code' => 'BOQ'],
                ['name' => 'Las Cocuizas', 'code' => 'LCO2'],
                ['name' => 'San Simón', 'code' => 'SSI2'],
                ['name' => 'Santa Cruz', 'code' => 'SCR2'],
                ['name' => 'San Vicente', 'code' => 'SVI2'],
            ],
            'MAY' => [ // Mayor
                ['name' => 'Santa Bárbara', 'code' => 'SBA2'],
            ],
            'PIA' => [ // Piar
                ['name' => 'Aragua de Maturín', 'code' => 'ADM'],
                ['name' => 'Chaguamal', 'code' => 'CHA4'],
            ],
            'PUN' => [ // Punceres
                ['name' => 'Quiriquire', 'code' => 'QUI3'],
            ],
            'SAN' => [ // Santa Bárbara
                ['name' => 'Santa Bárbara', 'code' => 'SBA3'],
            ],
            'SOT' => [ // Sotillo
                ['name' => 'Barrancas del Orinoco', 'code' => 'BDO'],
                ['name' => 'José Tadeo Monagas', 'code' => 'JTM'],
            ],
            'URA' => [ // Uracoa
                ['name' => 'Uracoa', 'code' => 'URA2'],
            ],

            // NUEVA ESPARTA - Todas las parroquias de todos los municipios
            'ANT' => [ // Antolín del Campo
                ['name' => 'San Juan Bautista', 'code' => 'SJB2'],
                ['name' => 'Zabala', 'code' => 'ZAB'],
            ],
            'ARI' => [ // Arismendi
                ['name' => 'La Asunción', 'code' => 'LAS'],
                ['name' => 'San Francisco', 'code' => 'SFR4'],
            ],
            'DIA' => [ // Díaz
                ['name' => 'San Juan Bautista', 'code' => 'SJB3'],
                ['name' => 'Zabala', 'code' => 'ZAB2'],
            ],
            'GAR' => [ // García
                ['name' => 'García', 'code' => 'GAR'],
                ['name' => 'Francisco Fajardo', 'code' => 'FFA'],
            ],
            'GOM' => [ // Gómez
                ['name' => 'Bolívar', 'code' => 'BOL3'],
                ['name' => 'Guevara', 'code' => 'GUE'],
                ['name' => 'Matasiete', 'code' => 'MAT2'],
                ['name' => 'Santa Ana', 'code' => 'SAN3'],
                ['name' => 'Sucre', 'code' => 'SUC3'],
            ],
            'MAN' => [ // Maneiro
                ['name' => 'Aguirre', 'code' => 'AGU3'],
                ['name' => 'Maneiro', 'code' => 'MAN2'],
            ],
            'MAR' => [ // Marcano
                ['name' => 'Adrián', 'code' => 'ADR'],
                ['name' => 'Juan Griego', 'code' => 'JGR'],
            ],
            'MRI' => [ // Mariño
                ['name' => 'Porlamar', 'code' => 'POR'],
            ],
            'PEN' => [ // Península de Macanao
                ['name' => 'Boca de Río', 'code' => 'BDR'],
                ['name' => 'San Francisco', 'code' => 'SFR5'],
            ],
            'TUB' => [ // Tubores
                ['name' => 'Pampatar', 'code' => 'PAM'],
                ['name' => 'Aguirre', 'code' => 'AGU4'],
            ],
            'VIL' => [ // Villalba
                ['name' => 'Vicente Fuentes', 'code' => 'VFU'],
                ['name' => 'Villalba', 'code' => 'VIL'],
            ],

            // PORTUGUESA - Todas las parroquias de todos los municipios
            'AGU' => [ // Agua Blanca
                ['name' => 'Agua Blanca', 'code' => 'ABL'],
            ],
            'ARA' => [ // Araure
                ['name' => 'Araure', 'code' => 'ARA2'],
                ['name' => 'Río Acarigua', 'code' => 'RAC'],
            ],
            'EST' => [ // Esteller
                ['name' => 'Píritu', 'code' => 'PIR2'],
                ['name' => 'Uveral', 'code' => 'UVE2'],
            ],
            'GUA' => [ // Guanare
                ['name' => 'Guanare', 'code' => 'GUA8'],
                ['name' => 'Córdoba', 'code' => 'COR'],
                ['name' => 'San José de la Montaña', 'code' => 'SJM'],
                ['name' => 'San Juan de Guanaguanare', 'code' => 'SJG2'],
                ['name' => 'Virgen de la Coromoto', 'code' => 'VCO'],
            ],
            'GUA' => [ // Guanarito
                ['name' => 'Guanarito', 'code' => 'GUA9'],
                ['name' => 'Trinidad de la Capilla', 'code' => 'TDC'],
                ['name' => 'Divina Pastora', 'code' => 'DPA'],
            ],
            'MON' => [ // Monseñor José Vicente de Unda
                ['name' => 'Chabasquén', 'code' => 'CHA5'],
                ['name' => 'Peña Blanca', 'code' => 'PBL'],
            ],
            'OSP' => [ // Ospino
                ['name' => 'Ospino', 'code' => 'OSP'],
                ['name' => 'Aparición', 'code' => 'APA2'],
                ['name' => 'La Estación', 'code' => 'LES2'],
            ],
            'PAE' => [ // Páez
                ['name' => 'Acarigua', 'code' => 'ACA2'],
                ['name' => 'Payara', 'code' => 'PAY'],
                ['name' => 'Pimpinela', 'code' => 'PIM'],
                ['name' => 'Ramón Peraza', 'code' => 'RPE'],
            ],
            'PAP' => [ // Papelón
                ['name' => 'Papelón', 'code' => 'PAP2'],
                ['name' => 'Caño Delgadito', 'code' => 'CDE'],
            ],
            'SCA' => [ // San Genaro de Boconoíto
                ['name' => 'Boconoíto', 'code' => 'BOC'],
                ['name' => 'Antolín Tovar', 'code' => 'ATO2'],
            ],
            'SRA' => [ // San Rafael de Onoto
                ['name' => 'San Rafael de Onoto', 'code' => 'SRO5'],
                ['name' => 'Santa Fe', 'code' => 'SFE2'],
                ['name' => 'Thermo Morles', 'code' => 'TMO'],
            ],
            'SCA' => [ // Santa Rosalía
                ['name' => 'Santa Rosalía', 'code' => 'SRO6'],
                ['name' => 'Florida', 'code' => 'FLO'],
                ['name' => 'El Playón', 'code' => 'EPL'],
            ],
            'SUC' => [ // Sucre
                ['name' => 'Biscucuy', 'code' => 'BIS'],
                ['name' => 'Concepción', 'code' => 'CON3'],
                ['name' => 'San Rafael de Palo Alzado', 'code' => 'SRP2'],
                ['name' => 'Uvencio Antonio Velásquez', 'code' => 'UAV'],
                ['name' => 'San José de Saguaz', 'code' => 'SJS'],
                ['name' => 'Villa Rosa', 'code' => 'VRO2'],
            ],
            'TUR' => [ // Turén
                ['name' => 'Villa Bruzual', 'code' => 'VBR'],
                ['name' => 'Canelones', 'code' => 'CAN4'],
                ['name' => 'Santa Cruz', 'code' => 'SCR3'],
                ['name' => 'San Isidro Labrador', 'code' => 'SIL'],
            ],

            // COJEDES - Todas las parroquias de todos los municipios
            'ANA' => [ // Anzoátegui
                ['name' => 'Cojedes', 'code' => 'COJ'],
                ['name' => 'Juan de Mata Suárez', 'code' => 'JMS2'],
            ],
            'GIR' => [ // Girardot
                ['name' => 'El Baúl', 'code' => 'EBA3'],
                ['name' => 'Sucre', 'code' => 'SUC4'],
            ],
            'LIM' => [ // Lima Blanco
                ['name' => 'Macapo', 'code' => 'MAC3'],
                ['name' => 'La Aguadita', 'code' => 'LAG3'],
            ],
            'PAO' => [ // Pao de San Juan Bautista
                ['name' => 'El Pao', 'code' => 'EPA3'],
            ],
            'RIC' => [ // Ricaurte
                ['name' => 'Libertad de Cojedes', 'code' => 'LDC'],
                ['name' => 'El Amparo', 'code' => 'EAM3'],
            ],
            'ROM' => [ // Rómulo Gallegos
                ['name' => 'Las Vegas', 'code' => 'LVE3'],
            ],
            'SCA' => [ // San Carlos
                ['name' => 'San Carlos de Austria', 'code' => 'SCA3'],
                ['name' => 'Juan Ángel Bravo', 'code' => 'JAB'],
                ['name' => 'Manuel Manrique', 'code' => 'MMA2'],
            ],
            'TIN' => [ // Tinaco
                ['name' => 'Tinaco', 'code' => 'TIN3'],
                ['name' => 'El Tinaco', 'code' => 'ETI2'],
            ],
            'TIN' => [ // Tinaquillo
                ['name' => 'Tinaquillo', 'code' => 'TIN4'],
                ['name' => 'General en Jefe José Laurencio Silva', 'code' => 'GJL'],
            ],

            // DELTA AMACURO - Todas las parroquias de todos los municipios
            'ANT' => [ // Antonio Díaz
                ['name' => 'Curiapo', 'code' => 'CUR2'],
                ['name' => 'Almirante Luis Brión', 'code' => 'ALB'],
                ['name' => 'Francisco Aniceto Lugo', 'code' => 'FAL'],
                ['name' => 'Manuel Renaud', 'code' => 'MRE'],
                ['name' => 'Padre Barral', 'code' => 'PBA'],
                ['name' => 'Santos de Abelgas', 'code' => 'SDA'],
            ],
            'CAS' => [ // Casacoima
                ['name' => 'Imataca', 'code' => 'IMA'],
                ['name' => 'Cinco de Julio', 'code' => '5DJ2'],
                ['name' => 'Juan Bautista Arismendi', 'code' => 'JBA'],
                ['name' => 'Manuel Piar', 'code' => 'MPI'],
                ['name' => 'Rómulo Gallegos', 'code' => 'RGA2'],
            ],
            'PED' => [ // Pedernales
                ['name' => 'Pedernales', 'code' => 'PED2'],
                ['name' => 'Luis Beltrán Prieto Figueroa', 'code' => 'LBP'],
            ],
            'TUC' => [ // Tucupita
                ['name' => 'San José', 'code' => 'SJO3'],
                ['name' => 'José Vidal Marcano', 'code' => 'JVM'],
                ['name' => 'Juan Millán', 'code' => 'JMI'],
                ['name' => 'Leonardo Ruíz Pineda', 'code' => 'LRP'],
                ['name' => 'Mariscal Antonio José de Sucre', 'code' => 'MAJ'],
                ['name' => 'Monseñor Argimiro García', 'code' => 'MAG'],
                ['name' => 'San Rafael', 'code' => 'SRA5'],
            ],

            // FALCÓN - Todas las parroquias de todos los municipios
            'ACO' => [ // Acosta
                ['name' => 'San Juan de los Cayos', 'code' => 'SJC2'],
                ['name' => 'Capadare', 'code' => 'CAP3'],
                ['name' => 'La Pastora', 'code' => 'LPA2'],
                ['name' => 'Libertador', 'code' => 'LIB3'],
            ],
            'BOL' => [ // Bolívar
                ['name' => 'San Luis', 'code' => 'SLU2'],
                ['name' => 'Aracua', 'code' => 'ARA3'],
                ['name' => 'La Peña', 'code' => 'LPE2'],
            ],
            'BUC' => [ // Buchivacoa
                ['name' => 'Capatárida', 'code' => 'CAP4'],
                ['name' => 'Bariro', 'code' => 'BAR4'],
                ['name' => 'Borojó', 'code' => 'BOR2'],
                ['name' => 'Guajiro', 'code' => 'GUA10'],
                ['name' => 'Seque', 'code' => 'SEQ'],
                ['name' => 'Zazárida', 'code' => 'ZAZ'],
                ['name' => 'Valle de Eroa', 'code' => 'VDE'],
            ],
            'CAC' => [ // Cacique Manaure
                ['name' => 'Yaracal', 'code' => 'YAR'],
                ['name' => 'Agua Clara', 'code' => 'ACL'],
                ['name' => 'Avaria', 'code' => 'AVA'],
                ['name' => 'Pedregal', 'code' => 'PED3'],
                ['name' => 'Piedra Grande', 'code' => 'PGR'],
                ['name' => 'Purureche', 'code' => 'PUR'],
            ],
            'CAR' => [ // Carirubana
                ['name' => 'Norte', 'code' => 'NOR2'],
                ['name' => 'Carirubana', 'code' => 'CAR8'],
                ['name' => 'Santa Ana', 'code' => 'SAN4'],
                ['name' => 'Urbana Punta Cardón', 'code' => 'UPC2'],
            ],
            'COL' => [ // Colina
                ['name' => 'La Vela de Coro', 'code' => 'LVC'],
                ['name' => 'Acurigua', 'code' => 'ACU'],
                ['name' => 'Guaibacoa', 'code' => 'GUA11'],
                ['name' => 'Las Calderas', 'code' => 'LCA3'],
                ['name' => 'Macoruca', 'code' => 'MAC4'],
            ],
            'COR' => [ // Coro
                ['name' => 'Coro', 'code' => 'COR2'],
                ['name' => 'Agua Larga', 'code' => 'ALA'],
                ['name' => 'Calibozo', 'code' => 'CAL2'],
                ['name' => 'Independencia', 'code' => 'IND3'],
                ['name' => 'Pedregal', 'code' => 'PED4'],
                ['name' => 'Pueblo Nuevo', 'code' => 'PNU3'],
                ['name' => 'San Antonio', 'code' => 'SAN5'],
                ['name' => 'San Gabriela', 'code' => 'SGA'],
                ['name' => 'Santa Ana de Coro', 'code' => 'SAC'],
                ['name' => 'Viento Libre', 'code' => 'VLI'],
            ],
            'DAB' => [ // Dabajuro
                ['name' => 'Dabajuro', 'code' => 'DAB'],
            ],
            'DEM' => [ // Democracia
                ['name' => 'Pedregal', 'code' => 'PED5'],
                ['name' => 'Agua Clara', 'code' => 'ACL2'],
                ['name' => 'Avaria', 'code' => 'AVA2'],
            ],
            'FAL' => [ // Falcón
                ['name' => 'Pueblo Nuevo de Falcón', 'code' => 'PNF'],
                ['name' => 'Jadacaquiva', 'code' => 'JAD'],
            ],
            'FED' => [ // Federación
                ['name' => 'Churuguara', 'code' => 'CHU2'],
                ['name' => 'Agua Larga', 'code' => 'ALA2'],
                ['name' => 'Independencia', 'code' => 'IND4'],
                ['name' => 'Mapararí', 'code' => 'MAP2'],
            ],
            'JAC' => [ // Jacura
                ['name' => 'Jacura', 'code' => 'JAC'],
            ],
            'LOS' => [ // Los Taques
                ['name' => 'Santa Cruz de Los Taques', 'code' => 'SCT2'],
                ['name' => 'Judibana', 'code' => 'JUD'],
                ['name' => 'Punta Cardón', 'code' => 'PCA2'],
            ],
            'MAU' => [ // Mauroa
                ['name' => 'Mene de Mauroa', 'code' => 'MDM'],
                ['name' => 'Casigua', 'code' => 'CAS3'],
                ['name' => 'San Félix', 'code' => 'SFE3'],
            ],
            'MIR' => [ // Miranda
                ['name' => 'Santa Ana de Coro', 'code' => 'SAC2'],
                ['name' => 'Sabaneta', 'code' => 'SAB'],
                ['name' => 'Pueblo Nuevo', 'code' => 'PNU4'],
            ],
            'MON' => [ // Monseñor Iturriza
                ['name' => 'Chichiriviche', 'code' => 'CHI5'],
                ['name' => 'Boca de Tocuyo', 'code' => 'BDT'],
                ['name' => 'Tocuyo de la Costa', 'code' => 'TDC2'],
            ],
            'PAL' => [ // Palmasola
                ['name' => 'Palma Sola', 'code' => 'PSO'],
            ],
            'PET' => [ // Petit
                ['name' => 'Cabure', 'code' => 'CAB3'],
                ['name' => 'Colina', 'code' => 'COL2'],
                ['name' => 'Curimagua', 'code' => 'CUR3'],
            ],
            'PIR' => [ // Píritu
                ['name' => 'Píritu', 'code' => 'PIR3'],
                ['name' => 'San José de la Costa', 'code' => 'SJC3'],
            ],
            'SAN' => [ // San Francisco
                ['name' => 'Mirimire', 'code' => 'MIR2'],
            ],
            'SIL' => [ // Silva
                ['name' => 'Tucacas', 'code' => 'TUC2'],
                ['name' => 'Boca de Aroa', 'code' => 'BDA'],
            ],
            'SOC' => [ // Sucre
                ['name' => 'La Cruz de Taratara', 'code' => 'LCT'],
                ['name' => 'Agua Linda', 'code' => 'ALI'],
                ['name' => 'Araurima', 'code' => 'ARA4'],
            ],
            'TOC' => [ // Tocópero
                ['name' => 'Tocópero', 'code' => 'TOC2'],
            ],
            'UNI' => [ // Unión
                ['name' => 'El Charal', 'code' => 'ECH2'],
                ['name' => 'Las Vegas del Tuy', 'code' => 'LVT'],
                ['name' => 'Santa Cruz de Bucaral', 'code' => 'SCB'],
            ],
            'URU' => [ // Urumaco
                ['name' => 'Urumaco', 'code' => 'URU'],
                ['name' => 'Bruzual', 'code' => 'BRU2'],
            ],
            'ZAM' => [ // Zamora
                ['name' => 'Puerto Cumarebo', 'code' => 'PCU'],
                ['name' => 'La Ciénaga', 'code' => 'LCI'],
                ['name' => 'La Soledad', 'code' => 'LSO'],
                ['name' => 'Pueblo Cumarebo', 'code' => 'PCU2'],
                ['name' => 'Zazárida', 'code' => 'ZAZ2'],
            ],

            // GUÁRICO - Todas las parroquias de todos los municipios
            'CAM' => [ // Camaguán
                ['name' => 'Camaguán', 'code' => 'CAM3'],
                ['name' => 'Puerto Miranda', 'code' => 'PMI'],
                ['name' => 'Uverito', 'code' => 'UVE3'],
            ],
            'CHA' => [ // Chaguaramas
                ['name' => 'Chaguaramas', 'code' => 'CHA6'],
            ],
            'COM' => [ // El Socorro
                ['name' => 'El Socorro', 'code' => 'ESO2'],
            ],
            'FRA' => [ // Francisco de Miranda
                ['name' => 'Calabozo', 'code' => 'CAL3'],
                ['name' => 'El Calvario', 'code' => 'ECA4'],
                ['name' => 'El Rastro', 'code' => 'ERA'],
                ['name' => 'Guardatinajas', 'code' => 'GUA12'],
            ],
            'GUA' => [ // Guayabal
                ['name' => 'Guayabal', 'code' => 'GUA13'],
                ['name' => 'Cazorla', 'code' => 'CAZ'],
            ],
            'INF' => [ // José Félix Ribas
                ['name' => 'Tucupido', 'code' => 'TUC3'],
                ['name' => 'San Rafael de Laya', 'code' => 'SRL'],
            ],
            'JTM' => [ // José Tadeo Monagas
                ['name' => 'Altagracia de Orituco', 'code' => 'ADO2'],
                ['name' => 'Lezama', 'code' => 'LEZ'],
                ['name' => 'Libertad de Orituco', 'code' => 'LDO2'],
                ['name' => 'Paso Real de Macaira', 'code' => 'PRM'],
                ['name' => 'San Francisco de Macaira', 'code' => 'SFM'],
                ['name' => 'San Rafael de Orituco', 'code' => 'SRO7'],
                ['name' => 'Soublette', 'code' => 'SOU'],
            ],
            'JGE' => [ // Juan Germán Roscio
                ['name' => 'San Juan de los Morros', 'code' => 'SJM2'],
                ['name' => 'Cantagallo', 'code' => 'CAN5'],
                ['name' => 'San Sebastián de los Reyes', 'code' => 'SSR'],
                ['name' => 'Parapara', 'code' => 'PAR2'],
            ],
            'JIN' => [ // Julián Mellado
                ['name' => 'El Sombrero', 'code' => 'ESO3'],
                ['name' => 'Sosa', 'code' => 'SOS2'],
            ],
            'LAS' => [ // Las Mercedes
                ['name' => 'Las Mercedes', 'code' => 'LME4'],
                ['name' => 'Cabruta', 'code' => 'CAB4'],
                ['name' => 'Santa Rita de Manapire', 'code' => 'SRM'],
            ],
            'LEO' => [ // Leonardo Infante
                ['name' => 'Valle de la Pascua', 'code' => 'VDP'],
                ['name' => 'Espino', 'code' => 'ESP2'],
            ],
            'ORT' => [ // Ortiz
                ['name' => 'Ortiz', 'code' => 'ORT'],
                ['name' => 'San Francisco de Tiznados', 'code' => 'SFT'],
                ['name' => 'San José de Tiznados', 'code' => 'SJT'],
                ['name' => 'San Lorenzo de Tiznados', 'code' => 'SLT2'],
            ],
            'SAN' => [ // San Gerónimo de Guayabal
                ['name' => 'Guayabal', 'code' => 'GUA14'],
                ['name' => 'Cazorla', 'code' => 'CAZ2'],
            ],
            'SJI' => [ // San José de Guaribe
                ['name' => 'San José de Guaribe', 'code' => 'SJG3'],
            ],
            'SMA' => [ // Santa María de Ipire
                ['name' => 'Santa María de Ipire', 'code' => 'SMI4'],
                ['name' => 'Altamira', 'code' => 'ALT3'],
            ],

            // SUCRE - Todas las parroquias de todos los municipios
            'AND' => [ // Andrés Eloy Blanco
                ['name' => 'Mariño', 'code' => 'MAR4'],
                ['name' => 'Rómulo Gallegos', 'code' => 'RGA3'],
            ],
            'AND' => [ // Andrés Mata
                ['name' => 'San José de Aerocuar', 'code' => 'SJA2'],
                ['name' => 'Tavera Acosta', 'code' => 'TAC2'],
            ],
            'ARI' => [ // Arismendi
                ['name' => 'Río Caribe', 'code' => 'RCA'],
                ['name' => 'Antonio José de Sucre', 'code' => 'AJS2'],
                ['name' => 'El Morro de Puerto Santo', 'code' => 'EMP'],
                ['name' => 'Puerto Santo', 'code' => 'PSA'],
                ['name' => 'San Juan de las Galdonas', 'code' => 'SJG4'],
            ],
            'BEN' => [ // Benítez
                ['name' => 'El Pilar', 'code' => 'EPI2'],
                ['name' => 'El Rincón', 'code' => 'ERI'],
                ['name' => 'General Francisco Antonio Doubleday', 'code' => 'GFA'],
                ['name' => 'Guaraúnos', 'code' => 'GUA15'],
                ['name' => 'Tunapuicito', 'code' => 'TUN'],
                ['name' => 'Unión', 'code' => 'UNI3'],
            ],
            'BER' => [ // Bermúdez
                ['name' => 'Carúpano', 'code' => 'CAR9'],
                ['name' => 'General en Jefe Santiago Mariño', 'code' => 'GSM'],
                ['name' => 'Libertad', 'code' => 'LIB4'],
                ['name' => 'Santa Catalina', 'code' => 'SCA4'],
                ['name' => 'Santa Rosa', 'code' => 'SRO8'],
                ['name' => 'Santa Teresa', 'code' => 'STE2'],
                ['name' => 'Macarapana', 'code' => 'MAC5'],
            ],
            'BOL' => [ // Bolívar
                ['name' => 'Marigüitar', 'code' => 'MAR5'],
                ['name' => 'Bolívar', 'code' => 'BOL4'],
                ['name' => 'Maracapana', 'code' => 'MAC6'],
                ['name' => 'Santa Catalina', 'code' => 'SCA5'],
                ['name' => 'Santa Rosa', 'code' => 'SRO9'],
                ['name' => 'Santa Teresa', 'code' => 'STE3'],
            ],
            'CAJ' => [ // Cajigal
                ['name' => 'Yaguraparo', 'code' => 'YAG3'],
                ['name' => 'Libertad', 'code' => 'LIB5'],
                ['name' => 'Paujil', 'code' => 'PAU'],
            ],
            'CRU' => [ // Cruz Salmerón Acosta
                ['name' => 'Araya', 'code' => 'ARA5'],
                ['name' => 'Chacopata', 'code' => 'CHA7'],
                ['name' => 'Manicuare', 'code' => 'MAN3'],
            ],
            'LIB' => [ // Libertador
                ['name' => 'Tunapuy', 'code' => 'TUN2'],
                ['name' => 'Campo de Carabobo', 'code' => 'CDC2'],
            ],
            'MAJ' => [ // Mariño
                ['name' => 'Irapa', 'code' => 'IRA'],
                ['name' => 'Campo Claro', 'code' => 'CCL'],
                ['name' => 'Maraval', 'code' => 'MAR6'],
                ['name' => 'San Antonio de Irapa', 'code' => 'SAI'],
                ['name' => 'Soro', 'code' => 'SOR'],
            ],
            'MEJ' => [ // Mejía
                ['name' => 'San Antonio del Golfo', 'code' => 'SAG3'],
            ],
            'MON' => [ // Montes
                ['name' => 'Cumanacoa', 'code' => 'CUM2'],
                ['name' => 'Arenas', 'code' => 'ARE2'],
                ['name' => 'Aricagua', 'code' => 'ARI5'],
                ['name' => 'Cocollar', 'code' => 'COC3'],
                ['name' => 'San Fernando', 'code' => 'SFE4'],
                ['name' => 'San Lorenzo', 'code' => 'SLO2'],
            ],
            'RIB' => [ // Ribero
                ['name' => 'Cariaco', 'code' => 'CAR10'],
                ['name' => 'Catuaro', 'code' => 'CAT4'],
                ['name' => 'Rendón', 'code' => 'REN'],
                ['name' => 'Santa Cruz', 'code' => 'SCR4'],
                ['name' => 'Santa María', 'code' => 'SMA2'],
            ],
            'SUC' => [ // Sucre
                ['name' => 'Altagracia', 'code' => 'ALT4'],
                ['name' => 'Santa Inés', 'code' => 'SIN3'],
                ['name' => 'Valentín Valiente', 'code' => 'VVA'],
                ['name' => 'Ayacucho', 'code' => 'AYA'],
                ['name' => 'San Juan', 'code' => 'SJU3'],
                ['name' => 'Gran Mariscal', 'code' => 'GMA2'],
                ['name' => 'Raúl Leoni', 'code' => 'RLE2'],
            ],
            'VAL' => [ // Valdez
                ['name' => 'Güiria', 'code' => 'GUI2'],
                ['name' => 'Cristóbal Colón', 'code' => 'CCO'],
                ['name' => 'Punta de Piedras', 'code' => 'PDP'],
                ['name' => 'Bideau', 'code' => 'BID'],
            ],

            // TÁCHIRA - Todas las parroquias de todos los municipios
            'AND' => [ // Andrés Bello
                ['name' => 'Cordero', 'code' => 'COR3'],
            ],
            'ANT' => [ // Antonio Rómulo Costa
                ['name' => 'Las Mesas', 'code' => 'LME5'],
            ],
            'AYA' => [ // Ayacucho
                ['name' => 'Colón', 'code' => 'COL3'],
                ['name' => 'Rivas Berti', 'code' => 'RBE2'],
                ['name' => 'San Pedro del Río', 'code' => 'SPR'],
            ],
            'BOL' => [ // Bolívar
                ['name' => 'San Antonio del Táchira', 'code' => 'SAT'],
                ['name' => 'Palotal', 'code' => 'PAL2'],
                ['name' => 'Juan Vicente Gómez', 'code' => 'JVG'],
                ['name' => 'Isaías Medina Angarita', 'code' => 'IMA2'],
            ],
            'CAR' => [ // Cárdenas
                ['name' => 'Táriba', 'code' => 'TAR'],
                ['name' => 'La Florida', 'code' => 'LFL'],
                ['name' => 'Amenodoro Ángel Lamus', 'code' => 'AAL'],
            ],
            'COR' => [ // Córdoba
                ['name' => 'Santa Ana del Táchira', 'code' => 'SAT2'],
                ['name' => 'Juan Vicente Gómez', 'code' => 'JVG2'],
                ['name' => 'Isaías Medina Angarita', 'code' => 'IMA3'],
            ],
            'FER' => [ // Fernández Feo
                ['name' => 'San Rafael de la Independencia', 'code' => 'SRI2'],
                ['name' => 'Juan Germán Roscio', 'code' => 'JGR2'],
                ['name' => 'Román Cárdenas', 'code' => 'RCA2'],
            ],
            'FRA' => [ // Francisco de Miranda
                ['name' => 'San José de Bolívar', 'code' => 'SJB4'],
                ['name' => 'El Cobre', 'code' => 'ECO2'],
                ['name' => 'Boca de Grita', 'code' => 'BDG'],
            ],
            'GAR' => [ // García de Hevia
                ['name' => 'La Fría', 'code' => 'LFR'],
                ['name' => 'Boca de Grita', 'code' => 'BDG2'],
                ['name' => 'José Antonio Páez', 'code' => 'JAP2'],
            ],
            'GUA' => [ // Guásimos
                ['name' => 'Palmira', 'code' => 'PAL3'],
            ],
            'IND' => [ // Independencia
                ['name' => 'Capacho Nuevo', 'code' => 'CAN6'],
                ['name' => 'Juan Germán Roscio', 'code' => 'JGR3'],
                ['name' => 'Román Cárdenas', 'code' => 'RCA3'],
            ],
            'JUA' => [ // Jáuregui
                ['name' => 'La Grita', 'code' => 'LGR'],
                ['name' => 'Monseñor Miguel Antonio Salas', 'code' => 'MMA3'],
            ],
            'JOS' => [ // José María Vargas
                ['name' => 'El Cobre', 'code' => 'ECO3'],
            ],
            'JUN' => [ // Junín
                ['name' => 'Rubio', 'code' => 'RUB'],
                ['name' => 'Bramon', 'code' => 'BRA'],
                ['name' => 'La Petrolea', 'code' => 'LPE3'],
                ['name' => 'Quinimari', 'code' => 'QUI4'],
            ],
            'LIB' => [ // Libertad
                ['name' => 'Capacho Viejo', 'code' => 'CAV'],
                ['name' => 'Cipriano Castro', 'code' => 'CCA'],
                ['name' => 'Manuel Felipe Rugeles', 'code' => 'MFR'],
            ],
            'LOR' => [ // Libertador
                ['name' => 'Abejales', 'code' => 'ABE2'],
                ['name' => 'Doradas', 'code' => 'DOR'],
                ['name' => 'Emeterio Ochoa', 'code' => 'EOC'],
                ['name' => 'San Joaquín de Navay', 'code' => 'SJN'],
            ],
            'LOB' => [ // Lobatera
                ['name' => 'Lobatera', 'code' => 'LOB'],
                ['name' => 'Constitución', 'code' => 'CON4'],
            ],
            'MIC' => [ // Michelena
                ['name' => 'Michelena', 'code' => 'MIC'],
            ],
            'PAN' => [ // Panamericano
                ['name' => 'Coloncito', 'code' => 'COL4'],
                ['name' => 'La Palmita', 'code' => 'LPA3'],
            ],
            'PED' => [ // Pedro María Ureña
                ['name' => 'Ureña', 'code' => 'URE'],
                ['name' => 'Nueva Arcadia', 'code' => 'NAR2'],
            ],
            'RAF' => [ // Rafael Urdaneta
                ['name' => 'Delicias', 'code' => 'DEL'],
            ],
            'SAM' => [ // Samuel Darío Maldonado
                ['name' => 'La Tendida', 'code' => 'LTE2'],
                ['name' => 'Bocono', 'code' => 'BOC2'],
                ['name' => 'Hernández', 'code' => 'HER2'],
            ],
            'SAN' => [ // San Cristóbal
                ['name' => 'La Concordia', 'code' => 'LCO3'],
                ['name' => 'Pedro María Morantes', 'code' => 'PMM2'],
                ['name' => 'San Juan Bautista', 'code' => 'SJB5'],
                ['name' => 'San Sebastián', 'code' => 'SSE2'],
                ['name' => 'Dr. Francisco Romero Lobo', 'code' => 'FRL2'],
            ],
            'SEG' => [ // Seboruco
                ['name' => 'Seboruco', 'code' => 'SEB'],
            ],
            'SIM' => [ // Simón Rodríguez
                ['name' => 'San Simón', 'code' => 'SSI3'],
            ],
            'SUC' => [ // Sucre
                ['name' => 'Queniquea', 'code' => 'QUE'],
                ['name' => 'San Pablo', 'code' => 'SPA2'],
                ['name' => 'Eleazar López Contreras', 'code' => 'ELC2'],
            ],
            'TOR' => [ // Torbes
                ['name' => 'San Josecito', 'code' => 'SJO4'],
            ],
            'URI' => [ // Uribante
                ['name' => 'Pregonero', 'code' => 'PRE'],
                ['name' => 'Cárdenas', 'code' => 'CAR11'],
                ['name' => 'Potosí', 'code' => 'POT2'],
                ['name' => 'Juan Pablo Peñaloza', 'code' => 'JPP'],
            ],

            // TRUJILLO - Todas las parroquias de todos los municipios
            'AND' => [ // Andrés Bello
                ['name' => 'Araguaney', 'code' => 'ARA6'],
                ['name' => 'El Jaguito', 'code' => 'EJA2'],
                ['name' => 'La Esperanza', 'code' => 'LES3'],
                ['name' => 'Santa Isabel', 'code' => 'SIS4'],
            ],
            'BOC' => [ // Boconó
                ['name' => 'Boconó', 'code' => 'BOC3'],
                ['name' => 'El Carmen', 'code' => 'ECA5'],
                ['name' => 'Mosquey', 'code' => 'MOS2'],
                ['name' => 'Ayacucho', 'code' => 'AYA2'],
                ['name' => 'Burbusay', 'code' => 'BUR3'],
                ['name' => 'General Ribas', 'code' => 'GRI'],
                ['name' => 'Guaramacal', 'code' => 'GUA16'],
                ['name' => 'La Vega de Guaramacal', 'code' => 'LVG'],
                ['name' => 'Monseñor Jáuregui', 'code' => 'MJA'],
                ['name' => 'Rafael Rangel', 'code' => 'RRA'],
                ['name' => 'San Miguel', 'code' => 'SMI5'],
                ['name' => 'San José', 'code' => 'SJO5'],
            ],
            'BOL' => [ // Bolívar
                ['name' => 'Sabana Grande', 'code' => 'SGR'],
                ['name' => 'Cheregüé', 'code' => 'CHE2'],
                ['name' => 'Granados', 'code' => 'GRA'],
            ],
            'CAN' => [ // Candelaria
                ['name' => 'Arnoldo Gabaldón', 'code' => 'AGA'],
                ['name' => 'Bolivia', 'code' => 'BOL5'],
                ['name' => 'Carrillo', 'code' => 'CAR12'],
                ['name' => 'Cegarra', 'code' => 'CEG'],
                ['name' => 'Chejendé', 'code' => 'CHE3'],
                ['name' => 'Manuel Salvador Ulloa', 'code' => 'MSU'],
                ['name' => 'San José', 'code' => 'SJO6'],
            ],
            'CAR' => [ // Carache
                ['name' => 'Carache', 'code' => 'CAR13'],
                ['name' => 'La Concepción', 'code' => 'LCO4'],
                ['name' => 'Cuicas', 'code' => 'CUI'],
                ['name' => 'Panamericana', 'code' => 'PAN2'],
                ['name' => 'Santa Cruz', 'code' => 'SCR5'],
            ],
            'ESC' => [ // Escuque
                ['name' => 'Escuque', 'code' => 'ESC'],
                ['name' => 'La Unión', 'code' => 'LUN2'],
                ['name' => 'Santa Rita', 'code' => 'SRI3'],
                ['name' => 'Sabana Libre', 'code' => 'SLI'],
            ],
            'JUA' => [ // Juan Vicente Campo Elías
                ['name' => 'Campo Elías', 'code' => 'CAM4'],
                ['name' => 'Arnoldo Gabaldón', 'code' => 'AGA2'],
            ],
            'LAR' => [ // La Ceiba
                ['name' => 'Santa Apolonia', 'code' => 'SAP2'],
                ['name' => 'El Progreso', 'code' => 'EPR'],
                ['name' => 'La Ceiba', 'code' => 'LCE'],
                ['name' => 'Tres de Febrero', 'code' => 'TDF'],
            ],
            'MIR' => [ // Miranda
                ['name' => 'El Dividive', 'code' => 'EDI'],
                ['name' => 'Agua Santa', 'code' => 'ASA2'],
                ['name' => 'Agua Caliente', 'code' => 'ACL3'],
                ['name' => 'El Cenizo', 'code' => 'ECE'],
                ['name' => 'Valerita', 'code' => 'VAL2'],
            ],
            'MON' => [ // Monte Carmelo
                ['name' => 'Monte Carmelo', 'code' => 'MCR'],
                ['name' => 'Buena Vista', 'code' => 'BVI3'],
                ['name' => 'Santa María del Horcón', 'code' => 'SMH'],
            ],
            'MOT' => [ // Motatán
                ['name' => 'Motatán', 'code' => 'MOT'],
                ['name' => 'El Baño', 'code' => 'EBA4'],
                ['name' => 'Jalisco', 'code' => 'JAL'],
            ],
            'PAM' => [ // Pampán
                ['name' => 'Pampán', 'code' => 'PAM2'],
                ['name' => 'Flor de Patria', 'code' => 'FDP'],
                ['name' => 'La Paz', 'code' => 'LPA4'],
                ['name' => 'Santa Ana', 'code' => 'SAN6'],
            ],
            'PAM' => [ // Pampanito
                ['name' => 'Pampanito', 'code' => 'PAM3'],
                ['name' => 'La Concepción', 'code' => 'LCO5'],
                ['name' => 'Pampanito II', 'code' => 'PA2'],
            ],
            'SUC' => [ // Sucre
                ['name' => 'Sabana de Mendoza', 'code' => 'SDM'],
                ['name' => 'Junín', 'code' => 'JUN2'],
                ['name' => 'Valmore Rodríguez', 'code' => 'VRO3'],
                ['name' => 'El Socorro', 'code' => 'ESO4'],
            ],
            'TRU' => [ // Trujillo
                ['name' => 'Trujillo', 'code' => 'TRU'],
                ['name' => 'Andrés Linares', 'code' => 'ALI2'],
                ['name' => 'Chiquinquirá', 'code' => 'CHI6'],
                ['name' => 'Cristóbal Mendoza', 'code' => 'CME'],
                ['name' => 'Cruz Carrillo', 'code' => 'CCR'],
                ['name' => 'Matriz', 'code' => 'MAT3'],
                ['name' => 'Monseñor Carrillo', 'code' => 'MCA'],
                ['name' => 'Tres Esquinas', 'code' => 'TES'],
            ],
            'URD' => [ // Urdaneta
                ['name' => 'Cabimbú', 'code' => 'CAB5'],
                ['name' => 'Jajó', 'code' => 'JAJ2'],
                ['name' => 'La Mesa de Esnujaque', 'code' => 'LME6'],
                ['name' => 'Santiago', 'code' => 'SAN7'],
                ['name' => 'Tuñame', 'code' => 'TUN3'],
                ['name' => 'La Quebrada', 'code' => 'LQU'],
            ],
            'VAL' => [ // Valera
                ['name' => 'Juan Ignacio Montilla', 'code' => 'JIM2'],
                ['name' => 'La Beatriz', 'code' => 'LBE'],
                ['name' => 'La Puerta', 'code' => 'LPU'],
                ['name' => 'Mendoza del Valle de Momboy', 'code' => 'MVM'],
                ['name' => 'Mercedes Díaz', 'code' => 'MDI'],
                ['name' => 'San Luis', 'code' => 'SLU3'],
            ],

            // YARACUY - Todas las parroquias de todos los municipios
            'ARI' => [ // Arístides Bastidas
                ['name' => 'San Pablo', 'code' => 'SPA3'],
            ],
            'BOL' => [ // Bolívar
                ['name' => 'Aroa', 'code' => 'ARO'],
                ['name' => 'Capital Aroa', 'code' => 'CAR14'],
            ],
            'BRA' => [ // Bruzual
                ['name' => 'Chivacoa', 'code' => 'CHI7'],
                ['name' => 'Campo Elías', 'code' => 'CAM5'],
            ],
            'COC' => [ // Cocorote
                ['name' => 'Cocorote', 'code' => 'COC4'],
            ],
            'FAL' => [ // Farriar
                ['name' => 'El Guayabo', 'code' => 'EGU3'],
                ['name' => 'Farriar', 'code' => 'FAR2'],
            ],
            'IND' => [ // Independencia
                ['name' => 'Independencia', 'code' => 'IND5'],
                ['name' => 'Agua Salada', 'code' => 'ASA3'],
                ['name' => 'El Amparo', 'code' => 'EAM4'],
                ['name' => 'Palo Negro', 'code' => 'PNE2'],
            ],
            'JOS' => [ // José Antonio Páez
                ['name' => 'Sabana de Parra', 'code' => 'SDP2'],
            ],
            'LAR' => [ // La Trinidad
                ['name' => 'Boraure', 'code' => 'BOR3'],
                ['name' => 'Guama', 'code' => 'GUA17'],
                ['name' => 'Yaritagua', 'code' => 'YAR2'],
            ],
            'MAN' => [ // Manuel Monge
                ['name' => 'Yumare', 'code' => 'YUM'],
            ],
            'NIC' => [ // Nirgua
                ['name' => 'Nirgua', 'code' => 'NIR'],
                ['name' => 'Salom', 'code' => 'SAL2'],
                ['name' => 'Temerla', 'code' => 'TEM2'],
            ],
            'PEÑ' => [ // Peña
                ['name' => 'Yaritagua', 'code' => 'YAR3'],
                ['name' => 'San Andrés', 'code' => 'SAN8'],
            ],
            'SAN' => [ // San Felipe
                ['name' => 'San Felipe', 'code' => 'SFE5'],
                ['name' => 'Albarico', 'code' => 'ALB2'],
                ['name' => 'San Javier', 'code' => 'SJA3'],
            ],
            'SUC' => [ // Sucre
                ['name' => 'Guamacho', 'code' => 'GUA18'],
            ],
            'URD' => [ // Urdaneta
                ['name' => 'Urachiche', 'code' => 'URA3'],
                ['name' => 'El Isiro', 'code' => 'EIS'],
            ],
            'VER' => [ // Veroes
                ['name' => 'Farriar', 'code' => 'FAR3'],
                ['name' => 'El Guayabo', 'code' => 'EGU4'],
            ],
        ];

        return $parishes[$municipalityCode] ?? [];
    }

    private function getCentersByParish(string $parishCode): array
    {
        // Generate unique codes using a global counter to avoid duplicates
        $uniqueId1 = 'VC_' . str_pad(self::$centerCounter++, 6, '0', STR_PAD_LEFT);
        $uniqueId2 = 'VC_' . str_pad(self::$centerCounter++, 6, '0', STR_PAD_LEFT);

        return [
            [
                'name' => 'Basic School ' . $parishCode . ' 001',
                'code' => $uniqueId1,
                'address' => 'Example address for center ' . $parishCode . ' 001',
            ],
            [
                'name' => 'High School ' . $parishCode . ' 002',
                'code' => $uniqueId2,
                'address' => 'Example address for center ' . $parishCode . ' 002',
            ],
        ];
    }
}
