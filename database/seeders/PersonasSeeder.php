<?php

namespace Database\Seeders;

use App\Models\Person;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use App\Models\VotingCenter;
use Illuminate\Database\Seeder;

class PeopleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get locations to assign to people
        $states = State::with(['municipalities.parishes.votingCenters'])->get();

        if ($states->isEmpty()) {
            $this->command->warn('No states available. Run LocationsSeeder first.');
            return;
        }

        // Create some 1x10 leaders
        $leaders = [
            [
                'first_name' => '<PERSON>',
                'last_name' => '<PERSON>',
                'document_number' => 'V-12345678',
                'birth_date' => '1980-05-15',
                'gender' => 'F',
                'phone' => '0414-1234567',
                'email' => '<EMAIL>',
                'person_type' => 'militant',
                'is_leader_1x10' => true,
                'status' => 'active',
            ],
            [
                'first_name' => '<PERSON>',
                'last_name' => '<PERSON>',
                'document_number' => 'V-23456789',
                'birth_date' => '1975-08-22',
                'gender' => 'M',
                'phone' => '0424-2345678',
                'email' => '<EMAIL>',
                'person_type' => 'militant',
                'is_leader_1x10' => true,
                'status' => 'active',
            ],
            [
                'first_name' => 'Ana Beatriz',
                'last_name' => 'López Silva',
                'document_number' => '**********',
                'birth_date' => '1985-12-03',
                'gender' => 'F',
                'phone' => '0412-3456789',
                'email' => '<EMAIL>',
                'person_type' => 'militant',
                'is_leader_1x10' => true,
                'status' => 'active',
            ],
        ];

        $createdLeaders = [];
        foreach ($leaders as $leaderData) {
            $location = $this->getRandomLocation($states);
            $leaderData = array_merge($leaderData, $location);
            $createdLeaders[] = Person::create($leaderData);
        }

        // Create regular people and assign them to leaders
        $people = [
            [
                'first_name' => 'José Luis',
                'last_name' => 'Hernández Castro',
                'document_number' => '**********',
                'birth_date' => '1990-03-10',
                'gender' => 'M',
                'phone' => '0416-4567890',
                'email' => '<EMAIL>',
                'person_type' => 'voter',
                'status' => 'active',
            ],
            [
                'first_name' => 'Carmen Rosa',
                'last_name' => 'Díaz Morales',
                'document_number' => '**********',
                'birth_date' => '1988-07-18',
                'gender' => 'F',
                'phone' => '0414-5678901',
                'email' => '<EMAIL>',
                'person_type' => 'voter',
                'status' => 'active',
            ],
            [
                'first_name' => 'Roberto Carlos',
                'last_name' => 'Vargas Ruiz',
                'document_number' => 'V-67890123',
                'birth_date' => '1992-11-25',
                'gender' => 'M',
                'phone' => '0424-6789012',
                'person_type' => 'sympathizer',
                'status' => 'active',
            ],
            [
                'first_name' => 'Luisa Fernanda',
                'last_name' => 'Torres Jiménez',
                'document_number' => 'V-78901234',
                'birth_date' => '1987-04-12',
                'gender' => 'F',
                'phone' => '0412-7890123',
                'email' => '<EMAIL>',
                'person_type' => 'militant',
                'status' => 'active',
            ],
            [
                'first_name' => 'Miguel Ángel',
                'last_name' => 'Ramírez Soto',
                'document_number' => 'V-89012345',
                'birth_date' => '1983-09-08',
                'gender' => 'M',
                'phone' => '0416-8901234',
                'email' => '<EMAIL>',
                'person_type' => 'voter',
                'status' => 'active',
            ],
            [
                'first_name' => 'Patricia Elena',
                'last_name' => 'Mendoza Flores',
                'document_number' => 'V-90123456',
                'birth_date' => '1991-01-30',
                'gender' => 'F',
                'phone' => '0414-9012345',
                'person_type' => 'voter',
                'status' => 'active',
            ],
            [
                'first_name' => 'Fernando José',
                'last_name' => 'Castillo Vega',
                'document_number' => 'V-01234567',
                'birth_date' => '1986-06-14',
                'gender' => 'M',
                'phone' => '0424-0123456',
                'email' => '<EMAIL>',
                'person_type' => 'militant',
                'status' => 'active',
            ],
            [
                'first_name' => 'Gabriela María',
                'last_name' => 'Moreno Aguilar',
                'document_number' => 'V-12340567',
                'birth_date' => '1989-10-05',
                'gender' => 'F',
                'phone' => '0412-1234056',
                'person_type' => 'sympathizer',
                'status' => 'active',
            ],
            [
                'first_name' => 'Andrés Felipe',
                'last_name' => 'Guerrero Núñez',
                'document_number' => 'V-23450678',
                'birth_date' => '1984-12-20',
                'gender' => 'M',
                'phone' => '0416-2345067',
                'email' => '<EMAIL>',
                'person_type' => 'voter',
                'status' => 'active',
            ],
            [
                'first_name' => 'Valentina Isabel',
                'last_name' => 'Rojas Herrera',
                'document_number' => 'V-34560789',
                'birth_date' => '1993-02-28',
                'gender' => 'F',
                'phone' => '0414-3456078',
                'person_type' => 'voter',
                'status' => 'active',
            ],
        ];

        foreach ($people as $index => $personData) {
            $location = $this->getRandomLocation($states);
            $personData = array_merge($personData, $location);

            // Assign to a leader (distribute evenly)
            $leaderIndex = $index % count($createdLeaders);
            $personData['assigned_leader_id'] = $createdLeaders[$leaderIndex]->id;

            Person::create($personData);
        }

        $this->command->info('Created ' . count($leaders) . ' 1x10 leaders and ' . count($people) . ' regular people.');
    }

    private function getRandomLocation($states)
    {
        $state = $states->random();
        $municipality = $state->municipalities->random();
        $parish = $municipality->parishes->random();
        $votingCenter = $parish->votingCenters->random();

        return [
            'state_id' => $state->id,
            'municipality_id' => $municipality->id,
            'parish_id' => $parish->id,
            'voting_center_id' => $votingCenter->id,
            'voting_table' => str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT),
            'address' => 'Example address in ' . $parish->name . ', ' . $municipality->name,
        ];
    }
}
