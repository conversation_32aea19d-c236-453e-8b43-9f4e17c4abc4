<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('people', function (Blueprint $table) {
            $table->id();

            // Personal data
            $table->string('first_name');
            $table->string('last_name');
            $table->string('document_number', 20)->unique();
            $table->date('birth_date')->nullable();
            $table->enum('gender', ['M', 'F', 'O'])->nullable();

            // Contact information
            $table->string('phone', 20)->nullable();
            $table->string('secondary_phone', 20)->nullable();
            $table->string('email')->nullable();
            $table->text('address')->nullable();

            // Geographic location
            $table->foreignId('state_id')->nullable()->constrained('states')->onDelete('set null');
            $table->foreignId('municipality_id')->nullable()->constrained('municipalities')->onDelete('set null');
            $table->foreignId('parish_id')->nullable()->constrained('parishes')->onDelete('set null');

            // Electoral information
            $table->foreignId('voting_center_id')->nullable()->constrained('voting_centers')->onDelete('set null');
            $table->string('voting_table', 10)->nullable();

            // Role in the system
            $table->enum('person_type', ['militant', 'voter', 'sympathizer'])->default('voter');
            $table->boolean('is_leader_1x10')->default(false);
            $table->foreignId('assigned_leader_id')->nullable()->constrained('people')->onDelete('set null');

            // Relationship with system user
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');

            // Status and metadata
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->text('notes')->nullable();
            $table->json('additional_data')->nullable();

            $table->timestamps();

            // Indexes to optimize searches
            $table->index(['first_name', 'last_name']);
            $table->index(['document_number']);
            $table->index(['person_type']);
            $table->index(['is_leader_1x10']);
            $table->index(['status']);
            $table->index(['state_id', 'municipality_id', 'parish_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('people');
    }
};
