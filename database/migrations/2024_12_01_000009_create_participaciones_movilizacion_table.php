<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mobilization_participations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('person_id')->constrained('people')->onDelete('cascade');
            $table->foreignId('mobilization_id')->constrained('mobilizations')->onDelete('cascade');
            $table->enum('participation_status', ['invited', 'confirmed', 'attended', 'not_attended'])->default('invited');
            $table->datetime('confirmation_date')->nullable();
            $table->datetime('attendance_date')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->unique(['person_id', 'mobilization_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mobilization_participations');
    }
};
