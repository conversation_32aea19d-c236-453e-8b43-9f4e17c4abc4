<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, rename the table
        Schema::rename('personas', 'people');

        // Then update the columns
        Schema::table('people', function (Blueprint $table) {
            // Rename columns to English
            $table->renameColumn('nombres', 'first_name');
            $table->renameColumn('apellidos', 'last_name');
            $table->renameColumn('cedula', 'document_number');
            $table->renameColumn('fecha_nacimiento', 'birth_date');
            $table->renameColumn('genero', 'gender');
            $table->renameColumn('telefono', 'phone');
            $table->renameColumn('telefono_secundario', 'secondary_phone');
            $table->renameColumn('direccion', 'address');
            $table->renameColumn('tipo_persona', 'person_type');
            $table->renameColumn('es_lider_1x10', 'is_leader_1x10');
            $table->renameColumn('lider_asignado_id', 'assigned_leader_id');
            $table->renameColumn('observaciones', 'notes');
            $table->renameColumn('datos_adicionales', 'additional_data');
        });

        // Update foreign key references
        Schema::table('people', function (Blueprint $table) {
            $table->renameColumn('estado_id', 'state_id');
            $table->renameColumn('municipio_id', 'municipality_id');
            $table->renameColumn('parroquia_id', 'parish_id');
            $table->renameColumn('centro_votacion_id', 'voting_center_id');
            $table->renameColumn('mesa_votacion', 'voting_table');
        });

        // Update enum values to English - First update the data
        DB::statement("UPDATE people SET person_type = CASE
            WHEN person_type = 'militante' THEN 'militant'
            WHEN person_type = 'votante' THEN 'voter'
            WHEN person_type = 'simpatizante' THEN 'sympathizer'
            ELSE person_type END");

        DB::statement("UPDATE people SET status = CASE
            WHEN status = 'activo' THEN 'active'
            WHEN status = 'inactivo' THEN 'inactive'
            WHEN status = 'suspendido' THEN 'suspended'
            ELSE status END");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse enum values to Spanish
        Schema::table('people', function (Blueprint $table) {
            $table->dropColumn('person_type');
            $table->dropColumn('status');
            $table->dropColumn('gender');
        });

        Schema::table('people', function (Blueprint $table) {
            $table->enum('person_type', ['militante', 'votante', 'simpatizante'])->default('votante')->after('voting_table');
            $table->enum('status', ['activo', 'inactivo', 'suspendido'])->default('activo')->after('additional_data');
            $table->enum('gender', ['M', 'F', 'O'])->nullable()->after('birth_date');
        });

        // Reverse foreign key references
        Schema::table('people', function (Blueprint $table) {
            $table->renameColumn('state_id', 'estado_id');
            $table->renameColumn('municipality_id', 'municipio_id');
            $table->renameColumn('parish_id', 'parroquia_id');
            $table->renameColumn('voting_center_id', 'centro_votacion_id');
            $table->renameColumn('voting_table', 'mesa_votacion');
        });

        // Reverse column names to Spanish
        Schema::table('people', function (Blueprint $table) {
            $table->renameColumn('first_name', 'nombres');
            $table->renameColumn('last_name', 'apellidos');
            $table->renameColumn('document_number', 'cedula');
            $table->renameColumn('birth_date', 'fecha_nacimiento');
            $table->renameColumn('phone', 'telefono');
            $table->renameColumn('secondary_phone', 'telefono_secundario');
            $table->renameColumn('address', 'direccion');
            $table->renameColumn('is_leader_1x10', 'es_lider_1x10');
            $table->renameColumn('assigned_leader_id', 'lider_asignado_id');
            $table->renameColumn('notes', 'observaciones');
            $table->renameColumn('additional_data', 'datos_adicionales');
        });

        // Finally, rename the table back
        Schema::rename('people', 'personas');
    }
};
