<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use App\Models\VotingCenter;

echo "=== VERIFICACIÓN DE PARROQUIAS AGREGADAS ===\n\n";

echo "📊 RESUMEN GENERAL:\n";
echo "- Estados: " . State::count() . "\n";
echo "- Municipios: " . Municipality::count() . "\n";
echo "- Parroquias: " . Parish::count() . "\n";
echo "- Centros de Votación: " . VotingCenter::count() . "\n\n";

echo "🏛️ PARROQUIAS POR ESTADO (con parroquias):\n";

$statesWithParishes = State::whereHas('municipalities.parishes')->with(['municipalities.parishes'])->get();

foreach ($statesWithParishes as $state) {
    $totalParishes = $state->municipalities->sum(function($municipality) {
        return $municipality->parishes->count();
    });
    
    if ($totalParishes > 0) {
        echo "- {$state->name} ({$state->code}): {$totalParishes} parroquias\n";
        
        // Mostrar algunos municipios con parroquias
        foreach ($state->municipalities->take(3) as $municipality) {
            if ($municipality->parishes->count() > 0) {
                echo "  └─ {$municipality->name}: {$municipality->parishes->count()} parroquias\n";
                foreach ($municipality->parishes->take(3) as $parish) {
                    echo "     • {$parish->name} ({$parish->code})\n";
                }
                if ($municipality->parishes->count() > 3) {
                    echo "     • ... y " . ($municipality->parishes->count() - 3) . " más\n";
                }
            }
        }
        echo "\n";
    }
}

echo "✅ PARROQUIAS AGREGADAS EXITOSAMENTE!\n";
echo "Se han agregado parroquias para los principales municipios de Venezuela.\n";
