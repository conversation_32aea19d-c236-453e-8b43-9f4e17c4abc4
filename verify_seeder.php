<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use App\Models\VotingCenter;

echo "=== VERIFICACIÓN DEL SEEDER DE UBICACIONES ===\n\n";

echo "📊 RESUMEN GENERAL:\n";
echo "- Estados: " . State::count() . "\n";
echo "- Municipios: " . Municipality::count() . "\n";
echo "- Parroquias: " . Parish::count() . "\n";
echo "- Centros de Votación: " . VotingCenter::count() . "\n\n";

echo "🗺️ ALGUNOS ESTADOS Y SUS MUNICIPIOS:\n";

$states = State::with('municipalities')->orderBy('name')->take(5)->get();
foreach ($states as $state) {
    echo "- {$state->name} ({$state->code}): {$state->municipalities->count()} municipios\n";
}

echo "\n🏛️ MUNICIPIOS DE ZULIA (primeros 5):\n";
$zulia = State::where('code', 'ZU')->with('municipalities')->first();
foreach ($zulia->municipalities->take(5) as $municipality) {
    echo "- {$municipality->name} ({$municipality->code})\n";
}

echo "\n🏛️ MUNICIPIOS DE MIRANDA (primeros 5):\n";
$miranda = State::where('code', 'MI')->with('municipalities')->first();
foreach ($miranda->municipalities->take(5) as $municipality) {
    echo "- {$municipality->name} ({$municipality->code})\n";
}

echo "\n✅ SEEDER EJECUTADO EXITOSAMENTE!\n";
echo "Se han creado todos los 24 estados de Venezuela con sus 335 municipios.\n";
