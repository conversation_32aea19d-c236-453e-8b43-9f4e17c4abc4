<?php

return [
    'title' => 'People Management',
    'title_description' => 'Manage all information related to people registered in the system',

    // Navigation and menus
    'listing' => 'Listing',
    'add_person' => 'Add Person',
    'advanced_search' => 'Advanced Search',
    'person_details' => 'Person Details',
    'edit_person' => 'Edit Person',

    // Tabs
    'all' => 'All',
    'militants' => 'Militants',
    'voters' => 'Voters',
    'sympathizers' => 'Sympathizers',
    'leaders_1x10' => '1x10 Leaders',

    // Personal data fields
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'full_name' => 'Full Name',
    'document_number' => 'Document Number',
    'birth_date' => 'Birth Date',
    'age' => 'Age',
    'gender' => 'Gender',
    'phone' => 'Phone',
    'secondary_phone' => 'Secondary Phone',
    'email' => 'Email',
    'address' => 'Address',

    // Gender options
    'male' => 'Male',
    'female' => 'Female',
    'other' => 'Other',

    // Geographic location
    'location' => 'Location',
    'state' => 'State',
    'municipality' => 'Municipality',
    'parish' => 'Parish',
    'select_state' => 'Select State',
    'select_municipality' => 'Select Municipality',
    'select_parish' => 'Select Parish',

    // Electoral information
    'electoral_info' => 'Electoral Information',
    'voting_center' => 'Voting Center',
    'voting_table' => 'Voting Table',
    'select_voting_center' => 'Select Voting Center',

    // Person type and role
    'person_type' => 'Person Type',
    'militant' => 'Militant',
    'voter' => 'Voter',
    'sympathizer' => 'Sympathizer',
    'is_leader_1x10' => 'Is 1x10 Leader',
    'assigned_leader' => 'Assigned Leader',
    'select_leader' => 'Select Leader',

    // Status
    'status' => 'Status',
    'person_status' => 'Person Status',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'suspended' => 'Suspended',

    // Actions
    'save' => 'Save',
    'update' => 'Update',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'view_details' => 'View Details',
    'edit' => 'Edit',
    'create_user' => 'Create User',
    'assign_leader' => 'Assign Leader',
    'export' => 'Export',
    'import' => 'Import',

    // Filters and search
    'search' => 'Search',
    'filter_by' => 'Filter by',
    'clear_filters' => 'Clear Filters',
    'search_by_name' => 'Search by name, document or phone',
    'filter_by_location' => 'Filter by location',
    'filter_by_role' => 'Filter by role',
    'filter_by_status' => 'Filter by status',
    'filter_by_leadership' => 'Filter by leadership',
    'only_leaders_1x10' => 'Only 1x10 leaders',
    'non_leaders' => 'Non-leaders',

    // Messages
    'no_results' => 'No people found with the applied filters',
    'person_created' => 'Person created successfully',
    'person_updated' => 'Person updated successfully',
    'person_deleted' => 'Person deleted successfully',
    'user_created' => 'System user created successfully',
    'leader_assigned' => 'Leader assigned successfully',

    // Validations
    'first_name_required' => 'First name is required',
    'last_name_required' => 'Last name is required',
    'document_number_required' => 'Document number is required',
    'document_number_unique' => 'Document number already exists',
    'email_unique' => 'Email already exists',
    'invalid_birth_date' => 'Invalid birth date',
    'invalid_phone_format' => 'Invalid phone format',

    // 1x10 Leadership
    'leadership_1x10' => '1x10 Leadership',
    'assigned_people' => 'Assigned People',
    'available_spaces' => 'Available Spaces',
    'max_people_reached' => 'Maximum number of people reached',
    'leader_without_spaces' => 'Selected leader has no available spaces',
    'cannot_be_own_leader' => 'A person cannot be their own leader',

    // Additional information
    'notes' => 'Notes',
    'additional_data' => 'Additional Data',
    'system_user' => 'System User',
    'has_user' => 'Has User',
    'no_user' => 'No User',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',

    // Statistics
    'total_people' => 'Total People',
    'total_militants' => 'Total Militants',
    'total_voters' => 'Total Voters',
    'total_sympathizers' => 'Total Sympathizers',
    'total_leaders' => 'Total Leaders',
    'active_people' => 'Active People',

    // Export/Import
    'export_in_development' => 'Export functionality in development',
    'import_people' => 'Import People',
    'download_template' => 'Download Template',
    'upload_file' => 'Upload File',

    // Permissions
    'view_people' => 'View People',
    'create_people' => 'Create People',
    'update_people' => 'Update People',
    'delete_people' => 'Delete People',
    'export_people' => 'Export People',
    'import_people_permission' => 'Import People',
    'assign_leaders' => 'Assign Leaders',
    'create_users_from_people' => 'Create Users from People',
];
