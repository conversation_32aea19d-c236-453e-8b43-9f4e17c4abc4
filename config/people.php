<?php

return [
    /*
    |--------------------------------------------------------------------------
    | People Management Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the people management module.
    | Here you can configure various aspects of how people are managed
    | in the system.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | 1x10 Leadership Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the 1x10 leadership methodology where each leader
    | can manage up to 10 people.
    |
    */
    'leadership_1x10' => [
        'enabled' => true,
        'max_people_per_leader' => 10,
        'allow_auto_assignment' => true,
        'require_leader_confirmation' => false,
        'allow_leader_change' => true,
        'notify_on_assignment' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Data Validations
    |--------------------------------------------------------------------------
    |
    | Configuration for people data validations.
    |
    */
    'validations' => [
        'document_number' => [
            'required' => true,
            'unique' => true,
            'format' => '/^[VEJ]-\d{7,8}$/', // Venezuelan format
        ],
        'email' => [
            'required' => false,
            'unique' => true,
        ],
        'phone' => [
            'required' => false,
            'format' => '/^0\d{3}-\d{7}$/', // Venezuelan format
        ],
        'birth_date' => [
            'min_age' => 16,
            'max_age' => 120,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Person Types
    |--------------------------------------------------------------------------
    |
    | Available person types in the system.
    |
    */
    'person_types' => [
        'militant' => [
            'label' => 'Militant',
            'description' => 'Active party member with full participation rights',
            'permissions' => ['vote', 'participate', 'lead'],
            'can_be_leader' => true,
        ],
        'voter' => [
            'label' => 'Voter',
            'description' => 'Registered voter who supports the party',
            'permissions' => ['vote', 'participate'],
            'can_be_leader' => false,
        ],
        'sympathizer' => [
            'label' => 'Sympathizer',
            'description' => 'Person who sympathizes with the party',
            'permissions' => ['participate'],
            'can_be_leader' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Status Configuration
    |--------------------------------------------------------------------------
    |
    | Available status for people in the system.
    |
    */
    'statuses' => [
        'active' => [
            'label' => 'Active',
            'description' => 'Person is active in the system',
            'color' => 'green',
        ],
        'inactive' => [
            'label' => 'Inactive',
            'description' => 'Person is temporarily inactive',
            'color' => 'yellow',
        ],
        'suspended' => [
            'label' => 'Suspended',
            'description' => 'Person is suspended from activities',
            'color' => 'red',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Import/Export Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for importing and exporting people data.
    |
    */
    'import_export' => [
        'enabled' => true,
        'max_import_rows' => 1000,
        'allowed_formats' => ['csv', 'xlsx'],
        'required_fields' => ['first_name', 'last_name', 'document_number'],
        'validate_on_import' => true,
        'create_backup_on_import' => true,
        'export_formats' => ['csv', 'xlsx', 'pdf'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Search Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for search functionality.
    |
    */
    'search' => [
        'enabled' => true,
        'searchable_fields' => [
            'first_name',
            'last_name',
            'document_number',
            'email',
            'phone',
        ],
        'min_search_length' => 2,
        'max_results' => 100,
        'enable_advanced_search' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Pagination Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for pagination in people listings.
    |
    */
    'pagination' => [
        'per_page_options' => [10, 15, 25, 50, 100],
        'default_per_page' => 15,
        'show_page_info' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Location Configuration
    |--------------------------------------------------------------------------
    |
    | Options for handling geographic locations.
    |
    */
    'locations' => [
        'require_complete_location' => false,
        'validate_location_hierarchy' => true,
        'allow_create_locations' => false,
        'cache_locations' => true,
        'cache_duration' => 3600, // 1 hour in seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Role Configuration
    |--------------------------------------------------------------------------
    |
    | Mapping of person types to system roles.
    |
    */
    'roles' => [
        'auto_mapping' => true,
        'type_mapping' => [
            'militant' => 'Militant',
            'voter' => 'Voter',
            'sympathizer' => 'Voter', // Sympathizers use voter role
        ],
        'leader_1x10_role' => 'Leader 1x10',
        'default_role' => 'Voter',
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for notifications related to people management.
    |
    */
    'notifications' => [
        'enabled' => true,
        'channels' => ['mail', 'database'],
        'events' => [
            'person_created' => true,
            'person_updated' => true,
            'person_deleted' => true,
            'leader_assigned' => true,
            'user_created_from_person' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | Security settings for people management.
    |
    */
    'security' => [
        'encrypt_sensitive_data' => false,
        'log_data_access' => true,
        'require_reason_for_deletion' => true,
        'soft_delete' => false,
        'data_retention_days' => null, // null = keep forever
    ],
];
